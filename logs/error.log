2025-08-07 09:10:12 | ERROR | src.database.connection:test_connection:85 | 數據庫連接測試失敗: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] 無法連線，因為目標電腦拒絕連線。)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 09:10:12 | ERROR | src.workstation_monitor:_initialize_components:97 | 組件初始化失敗: 數據庫連接失敗
2025-08-07 09:10:12 | ERROR | __main__:main:155 | 系統運行錯誤: 數據庫連接失敗
2025-08-07 09:51:43 | ERROR | src.database.connection:test_connection:85 | 數據庫連接測試失敗: (pymysql.err.OperationalError) (1045, "Access denied for user 'workstation_user'@'localhost' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 09:51:43 | ERROR | src.workstation_monitor:_initialize_components:97 | 組件初始化失敗: 數據庫連接失敗
2025-08-07 09:51:43 | ERROR | __main__:main:155 | 系統運行錯誤: 數據庫連接失敗
2025-08-07 09:57:22 | ERROR | src.database.connection:test_connection:85 | 數據庫連接測試失敗: (pymysql.err.OperationalError) (1045, "Access denied for user 'workstation_user'@'localhost' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 09:57:22 | ERROR | src.workstation_monitor:_initialize_components:97 | 組件初始化失敗: 數據庫連接失敗
2025-08-07 09:57:22 | ERROR | __main__:main:155 | 系統運行錯誤: 數據庫連接失敗
2025-08-07 10:08:47 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:47 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.plc.modbus_client:connect:78 | 無法連接到PLC: 10.28.40.10:502
2025-08-07 10:08:52 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.workstation_monitor:_on_plc_error:254 | PLC錯誤: PLC連接失敗 - 連接失敗
2025-08-07 10:08:52 | ERROR | src.plc.plc_manager:start_monitoring:123 | 無法連接PLC，監控啟動失敗
2025-08-07 10:08:52 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
