2025-08-07 09:10:08 | INFO | src.utils.logger_setup:setup_logger:70 | 日誌系統初始化完成，日誌級別: INFO
2025-08-07 09:10:08 | INFO | src.utils.logger_setup:setup_logger:71 | 日誌文件: logs\workstation_monitor.log
2025-08-07 09:10:08 | INFO | src.utils.logger_setup:setup_logger:72 | 錯誤日誌: logs\error.log
2025-08-07 09:10:08 | INFO | src.workstation_monitor:__init__:38 | 工站監控系統初始化開始
2025-08-07 09:10:08 | INFO | src.utils.config_manager:validate_config:159 | 配置驗證通過
2025-08-07 09:10:08 | INFO | src.database.connection:_initialize_engine:61 | 數據庫引擎初始化成功
2025-08-07 09:10:12 | ERROR | src.database.connection:test_connection:85 | 數據庫連接測試失敗: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] 無法連線，因為目標電腦拒絕連線。)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 09:10:12 | ERROR | src.workstation_monitor:_initialize_components:97 | 組件初始化失敗: 數據庫連接失敗
2025-08-07 09:10:12 | ERROR | __main__:main:155 | 系統運行錯誤: 數據庫連接失敗
2025-08-07 09:51:43 | INFO | src.utils.logger_setup:setup_logger:70 | 日誌系統初始化完成，日誌級別: INFO
2025-08-07 09:51:43 | INFO | src.utils.logger_setup:setup_logger:71 | 日誌文件: logs/workstation_monitor.log
2025-08-07 09:51:43 | INFO | src.utils.logger_setup:setup_logger:72 | 錯誤日誌: logs/error.log
2025-08-07 09:51:43 | INFO | src.workstation_monitor:__init__:38 | 工站監控系統初始化開始
2025-08-07 09:51:43 | INFO | src.utils.config_manager:validate_config:159 | 配置驗證通過
2025-08-07 09:51:43 | INFO | src.database.connection:_initialize_engine:61 | 數據庫引擎初始化成功
2025-08-07 09:51:43 | ERROR | src.database.connection:test_connection:85 | 數據庫連接測試失敗: (pymysql.err.OperationalError) (1045, "Access denied for user 'workstation_user'@'localhost' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 09:51:43 | ERROR | src.workstation_monitor:_initialize_components:97 | 組件初始化失敗: 數據庫連接失敗
2025-08-07 09:51:43 | ERROR | __main__:main:155 | 系統運行錯誤: 數據庫連接失敗
2025-08-07 09:57:22 | INFO | src.utils.logger_setup:setup_logger:70 | 日誌系統初始化完成，日誌級別: INFO
2025-08-07 09:57:22 | INFO | src.utils.logger_setup:setup_logger:71 | 日誌文件: logs/workstation_monitor.log
2025-08-07 09:57:22 | INFO | src.utils.logger_setup:setup_logger:72 | 錯誤日誌: logs/error.log
2025-08-07 09:57:22 | INFO | src.workstation_monitor:__init__:38 | 工站監控系統初始化開始
2025-08-07 09:57:22 | INFO | src.utils.config_manager:validate_config:159 | 配置驗證通過
2025-08-07 09:57:22 | INFO | src.database.connection:_initialize_engine:61 | 數據庫引擎初始化成功
2025-08-07 09:57:22 | ERROR | src.database.connection:test_connection:85 | 數據庫連接測試失敗: (pymysql.err.OperationalError) (1045, "Access denied for user 'workstation_user'@'localhost' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 09:57:22 | ERROR | src.workstation_monitor:_initialize_components:97 | 組件初始化失敗: 數據庫連接失敗
2025-08-07 09:57:22 | ERROR | __main__:main:155 | 系統運行錯誤: 數據庫連接失敗
2025-08-07 10:08:47 | INFO | src.utils.logger_setup:setup_logger:70 | 日誌系統初始化完成，日誌級別: INFO
2025-08-07 10:08:47 | INFO | src.utils.logger_setup:setup_logger:71 | 日誌文件: logs/workstation_monitor.log
2025-08-07 10:08:47 | INFO | src.utils.logger_setup:setup_logger:72 | 錯誤日誌: logs/error.log
2025-08-07 10:08:47 | INFO | src.workstation_monitor:__init__:38 | 工站監控系統初始化開始
2025-08-07 10:08:47 | INFO | src.utils.config_manager:validate_config:159 | 配置驗證通過
2025-08-07 10:08:47 | INFO | src.database.connection:_initialize_engine:61 | 數據庫引擎初始化成功
2025-08-07 10:08:47 | INFO | src.database.connection:test_connection:82 | 數據庫連接測試成功
2025-08-07 10:08:47 | INFO | src.database.connection:create_tables:92 | 數據表創建成功
2025-08-07 10:08:47 | INFO | src.database.data_manager:__init__:30 | 數據管理器初始化完成
2025-08-07 10:08:47 | INFO | src.plc.modbus_client:__init__:59 | Modbus TCP客戶端初始化: 10.28.40.10:502
2025-08-07 10:08:47 | INFO | src.plc.plc_manager:__init__:70 | PLC管理器初始化完成: 10.28.40.10:502
2025-08-07 10:08:47 | INFO | src.monitoring.station_monitor:__init__:97 | 工站監控器初始化完成，監控12個工站
2025-08-07 10:08:47 | INFO | src.monitoring.production_tracker:__init__:68 | 生產追蹤器初始化完成
2025-08-07 10:08:47 | INFO | src.plc.plc_manager:add_callback:82 | 添加回調函數: on_stay_trigger
2025-08-07 10:08:47 | INFO | src.plc.plc_manager:add_callback:82 | 添加回調函數: on_leave_trigger
2025-08-07 10:08:47 | INFO | src.plc.plc_manager:add_callback:82 | 添加回調函數: on_connection_change
2025-08-07 10:08:47 | INFO | src.plc.plc_manager:add_callback:82 | 添加回調函數: on_error
2025-08-07 10:08:47 | INFO | src.monitoring.station_monitor:add_callback:112 | 添加監控回調函數: on_station_arrival
2025-08-07 10:08:47 | INFO | src.monitoring.station_monitor:add_callback:112 | 添加監控回調函數: on_station_departure
2025-08-07 10:08:47 | INFO | src.monitoring.station_monitor:add_callback:112 | 添加監控回調函數: on_production_start
2025-08-07 10:08:47 | INFO | src.monitoring.station_monitor:add_callback:112 | 添加監控回調函數: on_production_end
2025-08-07 10:08:47 | INFO | src.monitoring.station_monitor:add_callback:112 | 添加監控回調函數: on_cycle_complete
2025-08-07 10:08:47 | INFO | src.monitoring.production_tracker:add_callback:74 | 添加追蹤回調函數: on_cycle_timeout
2025-08-07 10:08:47 | INFO | src.monitoring.production_tracker:add_callback:74 | 添加追蹤回調函數: on_statistics_update
2025-08-07 10:08:47 | INFO | src.workstation_monitor:_setup_callbacks:119 | 回調函數設置完成
2025-08-07 10:08:47 | INFO | src.workstation_monitor:_initialize_components:94 | 所有系統組件初始化完成
2025-08-07 10:08:47 | INFO | src.workstation_monitor:__init__:62 | 工站監控系統初始化完成
2025-08-07 10:08:47 | INFO | src.workstation_monitor:start:136 | 啟動工站監控系統...
2025-08-07 10:08:47 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:47 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.plc.modbus_client:connect:78 | 無法連接到PLC: 10.28.40.10:502
2025-08-07 10:08:52 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | INFO | src.workstation_monitor:_on_plc_connection_change:249 | PLC連接狀態變化: OFFLINE - PLC連接失敗
2025-08-07 10:08:52 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.workstation_monitor:_on_plc_error:254 | PLC錯誤: PLC連接失敗 - 連接失敗
2025-08-07 10:08:52 | ERROR | src.plc.plc_manager:start_monitoring:123 | 無法連接PLC，監控啟動失敗
2025-08-07 10:08:52 | INFO | src.workstation_monitor:start:151 | 工站監控系統啟動成功
2025-08-07 10:08:52 | INFO | src.workstation_monitor:_main_loop:192 | 進入主循環
2025-08-07 10:08:52 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:08:52 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | INFO | src.workstation_monitor:signal_handler:124 | 接收到信號 2，開始關閉系統...
2025-08-07 10:09:13 | INFO | src.workstation_monitor:shutdown:166 | 關閉工站監控系統...
2025-08-07 10:09:13 | INFO | src.plc.plc_manager:stop_monitoring:136 | PLC監控已停止
2025-08-07 10:09:13 | INFO | src.plc.modbus_client:disconnect:92 | PLC連接已斷開
2025-08-07 10:09:13 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | INFO | src.workstation_monitor:_on_plc_connection_change:249 | PLC連接狀態變化: OFFLINE - PLC連接已斷開
2025-08-07 10:09:13 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'MONITOR', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.connection:get_session:115 | 數據庫會話錯誤: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | ERROR | src.database.data_manager:update_system_status:165 | 更新系統狀態失敗: (pymysql.err.OperationalError) (1054, "Unknown column 'system_status.extra_data' in 'SELECT'")
[SQL: SELECT system_status.id AS system_status_id, system_status.component AS system_status_component, system_status.status AS system_status_status, system_status.last_update AS system_status_last_update, system_status.error_message AS system_status_error_message, system_status.extra_data AS system_status_extra_data, system_status.created_at AS system_status_created_at, system_status.updated_at AS system_status_updated_at 
FROM system_status 
WHERE system_status.component = %(component_1)s 
 LIMIT %(param_1)s]
[parameters: {'component_1': 'PLC', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-07 10:09:13 | INFO | src.database.connection:close:152 | 數據庫連接已關閉
2025-08-07 10:09:13 | INFO | src.workstation_monitor:shutdown:185 | 工站監控系統已關閉
2025-08-07 10:09:13 | INFO | src.workstation_monitor:_main_loop:210 | 主循環結束
