# 系統配置文件
system:
  name: "工站監控系統"
  version: "1.0.0"
  debug: true
  log_level: "INFO"

# PLC配置
plc:
  ip: "***********"
  port: 502
  timeout: 5
  retry_count: 3
  scan_interval: 1.0  # 掃描間隔(秒)
  
  # 寄存器映射
  registers:
    stay_trigger: 100    # M100 - 停留觸發
    leave_trigger: 101   # M101 - 離開觸發
    
  # 工站配置
  stations:
    total_count: 12
    sensor_mapping:
      # 工站編號對應的感測器輸入
      1: "X11"
      2: "X10" 
      3: "X9"
      4: "X8"
      5: "X7"
      6: "X6"
      7: "X5"
      8: "X4"
      9: "X3"
      10: "X2"
      11: "X1"
      12: "X0"

# 數據庫配置
database:
  host: "localhost"
  port: 3306
  username: "workstation_user"
  password: "workstation_pass"
  database: "workstation_monitoring"
  charset: "utf8mb4"
  
  # 連接池配置
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600
  
  # 表名配置
  tables:
    workstation_log: "workstation_log"

# 監控配置
monitoring:
  # 狀態檢測配置
  debounce_time: 0.1    # 防抖時間(秒)
  min_stay_time: 1.0    # 最小停留時間(秒)
  max_stay_time: 3600   # 最大停留時間(秒)
  
  # 生產週期配置
  production_timeout: 7200  # 生產超時時間(秒)
  final_station: 12         # 最終工站編號

# 日誌配置
logging:
  log_dir: "logs"
  log_file: "workstation_monitor.log"
  max_file_size: "10MB"
  backup_count: 5
  log_format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# 數據存儲配置
storage:
  batch_size: 100       # 批量插入大小
  flush_interval: 10    # 數據刷新間隔(秒)
  backup_enabled: true  # 是否啟用數據備份
  backup_interval: 3600 # 備份間隔(秒)
