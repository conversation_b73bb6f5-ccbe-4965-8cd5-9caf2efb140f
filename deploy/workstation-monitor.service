[Unit]
Description=工站停留與離開時間監控系統
Documentation=https://github.com/your-repo/workstation-monitor
After=network.target mariadb.service
Wants=mariadb.service

[Service]
Type=simple
User=workstation
Group=workstation
WorkingDirectory=/path/to/project
Environment=PATH=/path/to/project/venv/bin
ExecStart=/path/to/project/venv/bin/python /path/to/project/main.py --config /path/to/project/config/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=workstation-monitor

# 安全設置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/project/logs

# 資源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
