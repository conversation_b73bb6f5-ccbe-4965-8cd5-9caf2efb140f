#!/bin/bash
# 工站監控系統監控腳本 - 用於檢查系統狀態和健康度

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 獲取腳本所在目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}=========================================="
echo "工站監控系統狀態檢查"
echo -e "==========================================${NC}"

# 檢查系統服務狀態
echo -e "\n${BLUE}1. 系統服務狀態${NC}"
if systemctl is-active --quiet workstation-monitor; then
    echo -e "   監控服務: ${GREEN}運行中${NC}"
    
    # 獲取服務運行時間
    UPTIME=$(systemctl show workstation-monitor --property=ActiveEnterTimestamp --value)
    echo "   啟動時間: $UPTIME"
    
    # 獲取進程信息
    PID=$(systemctl show workstation-monitor --property=MainPID --value)
    if [ "$PID" != "0" ]; then
        CPU_MEM=$(ps -p $PID -o %cpu,%mem --no-headers)
        echo "   進程ID: $PID"
        echo "   CPU/內存使用: $CPU_MEM"
    fi
else
    echo -e "   監控服務: ${RED}未運行${NC}"
fi

if systemctl is-active --quiet mariadb; then
    echo -e "   數據庫服務: ${GREEN}運行中${NC}"
else
    echo -e "   數據庫服務: ${RED}未運行${NC}"
fi

# 檢查數據庫連接
echo -e "\n${BLUE}2. 數據庫連接${NC}"
cd "$PROJECT_DIR"
source venv/bin/activate

if python main.py --test-db > /dev/null 2>&1; then
    echo -e "   數據庫連接: ${GREEN}正常${NC}"
    
    # 獲取數據庫統計
    DB_STATS=$(mysql -u workstation_user -pworkstation_pass workstation_monitoring -e "
        SELECT 
            (SELECT COUNT(*) FROM workstation_log) as log_count,
            (SELECT COUNT(*) FROM production_cycle) as cycle_count,
            (SELECT COUNT(*) FROM workstation_log WHERE DATE(created_at) = CURDATE()) as today_logs
    " --skip-column-names 2>/dev/null)
    
    if [ ! -z "$DB_STATS" ]; then
        echo "   數據統計: $DB_STATS (總日誌/總週期/今日日誌)"
    fi
else
    echo -e "   數據庫連接: ${RED}失敗${NC}"
fi

# 檢查日誌文件
echo -e "\n${BLUE}3. 日誌文件狀態${NC}"
LOG_DIR="$PROJECT_DIR/logs"
if [ -d "$LOG_DIR" ]; then
    LOG_COUNT=$(find "$LOG_DIR" -name "*.log" | wc -l)
    echo "   日誌文件數量: $LOG_COUNT"
    
    if [ -f "$LOG_DIR/workstation_monitor.log" ]; then
        LOG_SIZE=$(du -h "$LOG_DIR/workstation_monitor.log" | cut -f1)
        LAST_LOG=$(tail -1 "$LOG_DIR/workstation_monitor.log" 2>/dev/null | cut -d'|' -f1 || echo "無法讀取")
        echo "   主日誌大小: $LOG_SIZE"
        echo "   最後日誌時間: $LAST_LOG"
    fi
    
    # 檢查錯誤日誌
    if [ -f "$LOG_DIR/error.log" ]; then
        ERROR_COUNT=$(wc -l < "$LOG_DIR/error.log" 2>/dev/null || echo "0")
        if [ "$ERROR_COUNT" -gt 0 ]; then
            echo -e "   錯誤日誌: ${YELLOW}$ERROR_COUNT 條錯誤${NC}"
        else
            echo -e "   錯誤日誌: ${GREEN}無錯誤${NC}"
        fi
    fi
else
    echo -e "   日誌目錄: ${RED}不存在${NC}"
fi

# 檢查系統資源
echo -e "\n${BLUE}4. 系統資源${NC}"
DISK_USAGE=$(df -h "$PROJECT_DIR" | awk 'NR==2 {print $5}')
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')
CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

echo "   磁盤使用率: $DISK_USAGE"
echo "   內存使用率: $MEMORY_USAGE"
echo "   CPU負載: $CPU_LOAD"

# 檢查網絡連接（PLC連接）
echo -e "\n${BLUE}5. 網絡連接${NC}"
PLC_IP=$(grep -A 5 "^plc:" "$PROJECT_DIR/config/config.yaml" | grep "ip:" | awk '{print $2}' | tr -d '"')
PLC_PORT=$(grep -A 5 "^plc:" "$PROJECT_DIR/config/config.yaml" | grep "port:" | awk '{print $2}')

if [ ! -z "$PLC_IP" ] && [ ! -z "$PLC_PORT" ]; then
    echo "   PLC地址: $PLC_IP:$PLC_PORT"
    
    if timeout 3 bash -c "</dev/tcp/$PLC_IP/$PLC_PORT" 2>/dev/null; then
        echo -e "   PLC連接: ${GREEN}可達${NC}"
    else
        echo -e "   PLC連接: ${RED}不可達${NC}"
    fi
else
    echo -e "   PLC配置: ${YELLOW}未找到${NC}"
fi

# 檢查最近的系統日誌
echo -e "\n${BLUE}6. 最近系統日誌${NC}"
if systemctl is-active --quiet workstation-monitor; then
    echo "   最近5條日誌:"
    journalctl -u workstation-monitor --no-pager -n 5 --output=short | sed 's/^/   /'
else
    echo -e "   ${YELLOW}服務未運行，無法獲取日誌${NC}"
fi

# 總結
echo -e "\n${BLUE}=========================================="
echo "狀態檢查完成"
echo -e "==========================================${NC}"

# 提供操作建議
echo -e "\n${BLUE}常用操作命令:${NC}"
echo "   查看實時日誌: sudo journalctl -u workstation-monitor -f"
echo "   重啟服務: sudo systemctl restart workstation-monitor"
echo "   查看詳細狀態: sudo systemctl status workstation-monitor"
echo "   手動運行: cd $PROJECT_DIR && source venv/bin/activate && python main.py"
echo ""
