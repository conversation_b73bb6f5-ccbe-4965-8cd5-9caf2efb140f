@echo off
REM 工站監控系統Windows安裝腳本
setlocal enabledelayedexpansion

echo ==========================================
echo 工站停留與離開時間監控系統安裝腳本 (Windows)
echo 版本: 1.0.0
echo ==========================================

REM 獲取腳本所在目錄
set "SCRIPT_DIR=%~dp0"
set "PROJECT_DIR=%SCRIPT_DIR%.."

echo 項目目錄: %PROJECT_DIR%
cd /d "%PROJECT_DIR%"

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: 未找到Python，請先安裝Python 3.8或更高版本
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 檢測到Python版本:
python --version

REM 創建虛擬環境
echo 創建Python虛擬環境...
python -m venv venv
if errorlevel 1 (
    echo 錯誤: 創建虛擬環境失敗
    pause
    exit /b 1
)

REM 激活虛擬環境
echo 激活虛擬環境...
call venv\Scripts\activate.bat

REM 升級pip
echo 升級pip...
python -m pip install --upgrade pip

REM 安裝Python依賴
echo 安裝Python依賴...
pip install -r requirements.txt
if errorlevel 1 (
    echo 錯誤: 安裝Python依賴失敗
    pause
    exit /b 1
)

REM 創建日誌目錄
echo 創建日誌目錄...
if not exist "logs" mkdir logs

REM 檢查MariaDB/MySQL是否可用
echo 檢查數據庫連接...
python main.py --test-db
if errorlevel 1 (
    echo 警告: 數據庫連接測試失敗
    echo 請確保已安裝並配置MariaDB或MySQL
    echo 下載地址: https://mariadb.org/download/
    echo.
    echo 數據庫配置步驟:
    echo 1. 安裝MariaDB
    echo 2. 創建數據庫: CREATE DATABASE workstation_monitoring;
    echo 3. 創建用戶: CREATE USER 'workstation_user'@'localhost' IDENTIFIED BY 'workstation_pass';
    echo 4. 授權: GRANT ALL PRIVILEGES ON workstation_monitoring.* TO 'workstation_user'@'localhost';
    echo 5. 執行初始化腳本: mysql -u workstation_user -pworkstation_pass workstation_monitoring ^< sql\init_database.sql
    echo.
    set /p continue="是否繼續安裝? (y/n): "
    if /i "!continue!" neq "y" (
        echo 安裝已取消
        pause
        exit /b 1
    )
)

echo.
echo ==========================================
echo 安裝完成！
echo ==========================================
echo.
echo 使用以下命令運行系統:
echo   cd %PROJECT_DIR%
echo   venv\Scripts\activate.bat
echo   python main.py
echo.
echo 配置文件位置: %PROJECT_DIR%\config\config.yaml
echo 日誌文件位置: %PROJECT_DIR%\logs\
echo.
echo 請根據實際環境修改配置文件中的PLC IP地址等參數
echo.

REM 詢問是否創建桌面快捷方式
set /p shortcut="是否創建桌面快捷方式? (y/n): "
if /i "%shortcut%" equ "y" (
    echo 創建桌面快捷方式...
    
    REM 創建批處理文件來啟動程序
    echo @echo off > "%PROJECT_DIR%\start_monitor.bat"
    echo cd /d "%PROJECT_DIR%" >> "%PROJECT_DIR%\start_monitor.bat"
    echo call venv\Scripts\activate.bat >> "%PROJECT_DIR%\start_monitor.bat"
    echo python main.py >> "%PROJECT_DIR%\start_monitor.bat"
    echo pause >> "%PROJECT_DIR%\start_monitor.bat"
    
    REM 創建快捷方式（需要PowerShell）
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\工站監控系統.lnk'); $Shortcut.TargetPath = '%PROJECT_DIR%\start_monitor.bat'; $Shortcut.WorkingDirectory = '%PROJECT_DIR%'; $Shortcut.Description = '工站停留與離開時間監控系統'; $Shortcut.Save()"
    
    echo 桌面快捷方式已創建
)

echo.
echo 安裝完成！按任意鍵退出...
pause >nul
