#!/bin/bash
# 工站監控系統備份腳本

set -e

echo "=========================================="
echo "工站監控系統備份腳本"
echo "=========================================="

# 獲取腳本所在目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 設置備份目錄
BACKUP_DIR="/home/<USER>/workstation_backups"
DATE_STR=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="workstation_backup_$DATE_STR"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"

echo "項目目錄: $PROJECT_DIR"
echo "備份目錄: $BACKUP_PATH"

# 創建備份目錄
mkdir -p "$BACKUP_PATH"

# 備份配置文件
echo "備份配置文件..."
cp -r "$PROJECT_DIR/config" "$BACKUP_PATH/"

# 備份日誌文件（最近7天）
echo "備份日誌文件..."
mkdir -p "$BACKUP_PATH/logs"
find "$PROJECT_DIR/logs" -name "*.log*" -mtime -7 -exec cp {} "$BACKUP_PATH/logs/" \;

# 備份數據庫
echo "備份數據庫..."
mysqldump -u workstation_user -pworkstation_pass workstation_monitoring > "$BACKUP_PATH/database_backup.sql"

# 備份系統服務文件
echo "備份系統服務文件..."
if [ -f "/etc/systemd/system/workstation-monitor.service" ]; then
    cp "/etc/systemd/system/workstation-monitor.service" "$BACKUP_PATH/"
fi

# 創建備份信息文件
echo "創建備份信息文件..."
cat > "$BACKUP_PATH/backup_info.txt" << EOF
工站監控系統備份信息
==================

備份時間: $(date)
備份版本: 1.0.0
系統信息: $(uname -a)
Python版本: $(python3 --version)

備份內容:
- 配置文件 (config/)
- 日誌文件 (logs/, 最近7天)
- 數據庫備份 (database_backup.sql)
- 系統服務文件 (workstation-monitor.service)

恢復說明:
1. 恢復配置文件: cp -r config/ /path/to/project/
2. 恢復數據庫: mysql -u workstation_user -pworkstation_pass workstation_monitoring < database_backup.sql
3. 恢復服務文件: sudo cp workstation-monitor.service /etc/systemd/system/
4. 重新加載服務: sudo systemctl daemon-reload
EOF

# 壓縮備份
echo "壓縮備份文件..."
cd "$BACKUP_DIR"
tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
rm -rf "$BACKUP_NAME"

# 清理舊備份（保留最近10個）
echo "清理舊備份..."
ls -t workstation_backup_*.tar.gz | tail -n +11 | xargs -r rm

echo ""
echo "=========================================="
echo "備份完成！"
echo "=========================================="
echo ""
echo "備份文件: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
echo "備份大小: $(du -h "$BACKUP_DIR/$BACKUP_NAME.tar.gz" | cut -f1)"
echo ""
echo "可用備份列表:"
ls -lh "$BACKUP_DIR"/workstation_backup_*.tar.gz | tail -5
echo ""
