#!/bin/bash
# 工站監控系統更新腳本

set -e

echo "=========================================="
echo "工站監控系統更新腳本"
echo "=========================================="

# 獲取腳本所在目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "項目目錄: $PROJECT_DIR"
cd "$PROJECT_DIR"

# 檢查服務狀態
if systemctl is-active --quiet workstation-monitor; then
    echo "停止監控服務..."
    sudo systemctl stop workstation-monitor
    SERVICE_WAS_RUNNING=true
else
    SERVICE_WAS_RUNNING=false
fi

# 備份當前配置
echo "備份當前配置..."
cp config/config.yaml config/config.yaml.backup.$(date +%Y%m%d_%H%M%S)

# 激活虛擬環境
echo "激活虛擬環境..."
source venv/bin/activate

# 更新Python依賴
echo "更新Python依賴..."
pip install --upgrade pip
pip install -r requirements.txt --upgrade

# 更新數據庫結構（如果需要）
echo "檢查數據庫更新..."
python main.py --test-db

if [ $? -ne 0 ]; then
    echo "警告: 數據庫連接測試失敗，請檢查配置"
fi

# 重新加載systemd配置
echo "重新加載systemd配置..."
sudo systemctl daemon-reload

# 如果服務之前在運行，重新啟動
if [ "$SERVICE_WAS_RUNNING" = true ]; then
    echo "重新啟動監控服務..."
    sudo systemctl start workstation-monitor
    
    # 等待服務啟動
    sleep 3
    
    if systemctl is-active --quiet workstation-monitor; then
        echo "✓ 服務重新啟動成功"
    else
        echo "✗ 服務重新啟動失敗，請檢查日誌"
        sudo journalctl -u workstation-monitor --no-pager -n 20
    fi
fi

echo ""
echo "=========================================="
echo "更新完成！"
echo "=========================================="
echo ""
echo "查看服務狀態: sudo systemctl status workstation-monitor"
echo "查看實時日誌: sudo journalctl -u workstation-monitor -f"
echo ""
