# 工站監控系統部署指南

## 系統要求

### 硬體要求
- 樹莓派 4B 或更高版本（推薦4GB RAM）
- 至少16GB SD卡（Class 10或更高）
- 網路連接（有線或無線）
- 與PLC的網路連通性

### 軟體要求
- Raspberry Pi OS (Debian 11/12) 或 Ubuntu 20.04+
- Python 3.8+
- MariaDB 10.5+
- 至少2GB可用磁盤空間

## 快速安裝

### 1. 下載項目
```bash
git clone <repository-url>
cd CT
```

### 2. 運行安裝腳本
```bash
chmod +x deploy/install.sh
./deploy/install.sh
```

### 3. 修改配置
編輯配置文件以匹配您的環境：
```bash
nano config/config.yaml
```

主要需要修改的配置項：
- `plc.ip`: PLC的IP地址
- `plc.port`: PLC的Modbus TCP端口
- `database.*`: 數據庫連接參數（如果需要）

### 4. 啟動服務
```bash
sudo systemctl start workstation-monitor
sudo systemctl enable workstation-monitor
```

## 手動安裝步驟

如果自動安裝腳本失敗，可以按照以下步驟手動安裝：

### 1. 安裝系統依賴
```bash
sudo apt update
sudo apt install -y python3 python3-pip python3-venv mariadb-server git
```

### 2. 設置Python環境
```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 3. 配置MariaDB
```bash
sudo systemctl start mariadb
sudo systemctl enable mariadb

# 創建數據庫和用戶
sudo mysql -e "CREATE DATABASE workstation_monitoring CHARACTER SET utf8mb4;"
sudo mysql -e "CREATE USER 'workstation_user'@'localhost' IDENTIFIED BY 'workstation_pass';"
sudo mysql -e "GRANT ALL PRIVILEGES ON workstation_monitoring.* TO 'workstation_user'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

# 初始化數據庫結構
mysql -u workstation_user -pworkstation_pass workstation_monitoring < sql/init_database.sql
```

### 4. 測試安裝
```bash
python main.py --test-db
```

### 5. 安裝系統服務
```bash
sudo cp deploy/workstation-monitor.service /etc/systemd/system/
sudo sed -i "s|/path/to/project|$(pwd)|g" /etc/systemd/system/workstation-monitor.service
sudo systemctl daemon-reload
sudo systemctl enable workstation-monitor
```

## 服務管理

### 基本命令
```bash
# 啟動服務
sudo systemctl start workstation-monitor

# 停止服務
sudo systemctl stop workstation-monitor

# 重啟服務
sudo systemctl restart workstation-monitor

# 查看狀態
sudo systemctl status workstation-monitor

# 查看日誌
sudo journalctl -u workstation-monitor -f
```

### 系統監控
使用提供的監控腳本檢查系統狀態：
```bash
./deploy/monitor.sh
```

## 維護操作

### 更新系統
```bash
./deploy/update.sh
```

### 備份數據
```bash
./deploy/backup.sh
```

### 查看日誌
```bash
# 查看應用日誌
tail -f logs/workstation_monitor.log

# 查看系統服務日誌
sudo journalctl -u workstation-monitor -f

# 查看錯誤日誌
tail -f logs/error.log
```

## 配置說明

### PLC配置
```yaml
plc:
  ip: "***********"        # PLC IP地址
  port: 502                # Modbus TCP端口
  timeout: 5               # 連接超時時間
  retry_count: 3           # 重試次數
  scan_interval: 1.0       # 掃描間隔（秒）
```

### 數據庫配置
```yaml
database:
  host: "localhost"
  port: 3306
  username: "workstation_user"
  password: "workstation_pass"
  database: "workstation_monitoring"
```

### 監控配置
```yaml
monitoring:
  debounce_time: 0.1       # 防抖時間（秒）
  min_stay_time: 1.0       # 最小停留時間（秒）
  max_stay_time: 3600      # 最大停留時間（秒）
  production_timeout: 7200 # 生產超時時間（秒）
  final_station: 12        # 最終工站編號
```

## 故障排除

### 常見問題

1. **數據庫連接失敗**
   - 檢查MariaDB服務是否運行：`sudo systemctl status mariadb`
   - 檢查用戶權限和密碼
   - 檢查防火牆設置

2. **PLC連接失敗**
   - 檢查網路連通性：`ping ***********`
   - 檢查PLC IP和端口配置
   - 檢查防火牆設置

3. **服務無法啟動**
   - 查看詳細錯誤：`sudo journalctl -u workstation-monitor -n 50`
   - 檢查配置文件語法
   - 檢查文件權限

4. **日誌文件過大**
   - 日誌會自動輪轉，但可以手動清理：`rm logs/*.log.zip`

### 日誌級別
可以在配置文件中調整日誌級別：
```yaml
system:
  log_level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR
```

## 性能優化

### 系統調優
1. 增加文件描述符限制
2. 調整數據庫連接池大小
3. 優化掃描間隔時間
4. 定期清理舊數據

### 監控指標
- CPU使用率
- 內存使用率
- 磁盤空間
- 網路連接狀態
- 數據庫性能

## 安全建議

1. 修改默認數據庫密碼
2. 配置防火牆規則
3. 定期更新系統
4. 定期備份數據
5. 監控系統日誌

## 支援

如遇到問題，請：
1. 查看系統日誌
2. 運行診斷腳本：`./deploy/monitor.sh`
3. 檢查配置文件
4. 聯繫技術支援
