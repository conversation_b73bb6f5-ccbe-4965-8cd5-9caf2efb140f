<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>a</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>0</td>
    </tr>
    <tr>
      <th>1</th>
      <td>1</td>
    </tr>
    <tr>
      <th>2</th>
      <td>2</td>
    </tr>
    <tr>
      <th>3</th>
      <td>3</td>
    </tr>
    <tr>
      <th>4</th>
      <td>4</td>
    </tr>
    <tr>
      <th>5</th>
      <td>5</td>
    </tr>
    <tr>
      <th>6</th>
      <td>6</td>
    </tr>
    <tr>
      <th>7</th>
      <td>7</td>
    </tr>
    <tr>
      <th>8</th>
      <td>8</td>
    </tr>
    <tr>
      <th>9</th>
      <td>9</td>
    </tr>
    <tr>
      <th>10</th>
      <td>10</td>
    </tr>
    <tr>
      <th>11</th>
      <td>11</td>
    </tr>
    <tr>
      <th>12</th>
      <td>12</td>
    </tr>
    <tr>
      <th>13</th>
      <td>13</td>
    </tr>
    <tr>
      <th>14</th>
      <td>14</td>
    </tr>
    <tr>
      <th>15</th>
      <td>15</td>
    </tr>
    <tr>
      <th>16</th>
      <td>16</td>
    </tr>
    <tr>
      <th>17</th>
      <td>17</td>
    </tr>
    <tr>
      <th>18</th>
      <td>18</td>
    </tr>
    <tr>
      <th>19</th>
      <td>19</td>
    </tr>
    <tr>
      <th>20</th>
      <td>20</td>
    </tr>
    <tr>
      <th>21</th>
      <td>21</td>
    </tr>
    <tr>
      <th>22</th>
      <td>22</td>
    </tr>
    <tr>
      <th>23</th>
      <td>23</td>
    </tr>
    <tr>
      <th>24</th>
      <td>24</td>
    </tr>
    <tr>
      <th>25</th>
      <td>25</td>
    </tr>
    <tr>
      <th>26</th>
      <td>26</td>
    </tr>
    <tr>
      <th>27</th>
      <td>27</td>
    </tr>
    <tr>
      <th>28</th>
      <td>28</td>
    </tr>
    <tr>
      <th>29</th>
      <td>29</td>
    </tr>
    <tr>
      <th>30</th>
      <td>30</td>
    </tr>
    <tr>
      <th>31</th>
      <td>31</td>
    </tr>
    <tr>
      <th>32</th>
      <td>32</td>
    </tr>
    <tr>
      <th>33</th>
      <td>33</td>
    </tr>
    <tr>
      <th>34</th>
      <td>34</td>
    </tr>
    <tr>
      <th>35</th>
      <td>35</td>
    </tr>
    <tr>
      <th>36</th>
      <td>36</td>
    </tr>
    <tr>
      <th>37</th>
      <td>37</td>
    </tr>
    <tr>
      <th>38</th>
      <td>38</td>
    </tr>
    <tr>
      <th>39</th>
      <td>39</td>
    </tr>
    <tr>
      <th>40</th>
      <td>40</td>
    </tr>
    <tr>
      <th>41</th>
      <td>41</td>
    </tr>
    <tr>
      <th>42</th>
      <td>42</td>
    </tr>
    <tr>
      <th>43</th>
      <td>43</td>
    </tr>
    <tr>
      <th>44</th>
      <td>44</td>
    </tr>
    <tr>
      <th>45</th>
      <td>45</td>
    </tr>
    <tr>
      <th>46</th>
      <td>46</td>
    </tr>
    <tr>
      <th>47</th>
      <td>47</td>
    </tr>
    <tr>
      <th>48</th>
      <td>48</td>
    </tr>
    <tr>
      <th>49</th>
      <td>49</td>
    </tr>
    <tr>
      <th>50</th>
      <td>50</td>
    </tr>
    <tr>
      <th>51</th>
      <td>51</td>
    </tr>
    <tr>
      <th>52</th>
      <td>52</td>
    </tr>
    <tr>
      <th>53</th>
      <td>53</td>
    </tr>
    <tr>
      <th>54</th>
      <td>54</td>
    </tr>
    <tr>
      <th>55</th>
      <td>55</td>
    </tr>
    <tr>
      <th>56</th>
      <td>56</td>
    </tr>
    <tr>
      <th>57</th>
      <td>57</td>
    </tr>
    <tr>
      <th>58</th>
      <td>58</td>
    </tr>
    <tr>
      <th>59</th>
      <td>59</td>
    </tr>
    <tr>
      <th>60</th>
      <td>60</td>
    </tr>
  </tbody>
</table>
</div>
