<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>a</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>0</td>
    </tr>
    <tr>
      <th>1</th>
      <td>1</td>
    </tr>
    <tr>
      <th>2</th>
      <td>2</td>
    </tr>
    <tr>
      <th>3</th>
      <td>3</td>
    </tr>
    <tr>
      <th>4</th>
      <td>4</td>
    </tr>
    <tr>
      <th>5</th>
      <td>5</td>
    </tr>
    <tr>
      <th>...</th>
      <td>...</td>
    </tr>
    <tr>
      <th>55</th>
      <td>55</td>
    </tr>
    <tr>
      <th>56</th>
      <td>56</td>
    </tr>
    <tr>
      <th>57</th>
      <td>57</td>
    </tr>
    <tr>
      <th>58</th>
      <td>58</td>
    </tr>
    <tr>
      <th>59</th>
      <td>59</td>
    </tr>
    <tr>
      <th>60</th>
      <td>60</td>
    </tr>
  </tbody>
</table>
<p>61 rows × 1 columns</p>
</div>
