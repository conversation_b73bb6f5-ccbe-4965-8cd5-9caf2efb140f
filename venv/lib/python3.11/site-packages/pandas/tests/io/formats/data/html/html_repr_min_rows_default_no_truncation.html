<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>a</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>0</td>
    </tr>
    <tr>
      <th>1</th>
      <td>1</td>
    </tr>
    <tr>
      <th>2</th>
      <td>2</td>
    </tr>
    <tr>
      <th>3</th>
      <td>3</td>
    </tr>
    <tr>
      <th>4</th>
      <td>4</td>
    </tr>
    <tr>
      <th>5</th>
      <td>5</td>
    </tr>
    <tr>
      <th>6</th>
      <td>6</td>
    </tr>
    <tr>
      <th>7</th>
      <td>7</td>
    </tr>
    <tr>
      <th>8</th>
      <td>8</td>
    </tr>
    <tr>
      <th>9</th>
      <td>9</td>
    </tr>
    <tr>
      <th>10</th>
      <td>10</td>
    </tr>
    <tr>
      <th>11</th>
      <td>11</td>
    </tr>
    <tr>
      <th>12</th>
      <td>12</td>
    </tr>
    <tr>
      <th>13</th>
      <td>13</td>
    </tr>
    <tr>
      <th>14</th>
      <td>14</td>
    </tr>
    <tr>
      <th>15</th>
      <td>15</td>
    </tr>
    <tr>
      <th>16</th>
      <td>16</td>
    </tr>
    <tr>
      <th>17</th>
      <td>17</td>
    </tr>
    <tr>
      <th>18</th>
      <td>18</td>
    </tr>
    <tr>
      <th>19</th>
      <td>19</td>
    </tr>
  </tbody>
</table>
</div>
