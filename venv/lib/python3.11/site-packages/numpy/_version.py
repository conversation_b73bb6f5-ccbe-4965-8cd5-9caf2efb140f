
# This file was generated by 'versioneer.py' (0.26) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-07-30T18:52:43-0600",
 "dirty": false,
 "error": null,
 "full-revisionid": "ea677928332c37e8052b4d599bf6ee52cf363cf9",
 "version": "1.25.2"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
