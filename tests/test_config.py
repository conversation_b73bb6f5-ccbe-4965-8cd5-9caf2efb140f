"""
配置管理器測試
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from src.utils.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器測試類"""
    
    def test_load_config_success(self):
        """測試成功加載配置"""
        # 創建臨時配置文件
        config_data = {
            'system': {'name': 'test', 'debug': True},
            'plc': {'ip': '*************', 'port': 502}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = ConfigManager(temp_path)
            assert config_manager.get('system.name') == 'test'
            assert config_manager.get('plc.ip') == '*************'
            assert config_manager.get('plc.port') == 502
        finally:
            Path(temp_path).unlink()
    
    def test_get_with_default(self):
        """測試獲取配置值（帶默認值）"""
        config_data = {'test': {'value': 123}}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = ConfigManager(temp_path)
            assert config_manager.get('test.value') == 123
            assert config_manager.get('test.missing', 'default') == 'default'
            assert config_manager.get('missing.key', None) is None
        finally:
            Path(temp_path).unlink()
    
    def test_get_section(self):
        """測試獲取配置段"""
        config_data = {
            'database': {
                'host': 'localhost',
                'port': 3306,
                'username': 'user'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = ConfigManager(temp_path)
            db_config = config_manager.get_section('database')
            assert db_config['host'] == 'localhost'
            assert db_config['port'] == 3306
            assert db_config['username'] == 'user'
        finally:
            Path(temp_path).unlink()
    
    def test_validate_config(self):
        """測試配置驗證"""
        # 完整配置
        complete_config = {
            'system': {'name': 'test'},
            'plc': {'ip': '*************', 'port': 502},
            'database': {
                'host': 'localhost',
                'port': 3306,
                'username': 'user',
                'password': 'pass',
                'database': 'test_db'
            },
            'monitoring': {},
            'logging': {}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(complete_config, f)
            temp_path = f.name
        
        try:
            config_manager = ConfigManager(temp_path)
            assert config_manager.validate_config() is True
        finally:
            Path(temp_path).unlink()
        
        # 不完整配置
        incomplete_config = {
            'system': {'name': 'test'},
            'plc': {'ip': '*************'}  # 缺少port
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(incomplete_config, f)
            temp_path = f.name
        
        try:
            config_manager = ConfigManager(temp_path)
            assert config_manager.validate_config() is False
        finally:
            Path(temp_path).unlink()


if __name__ == "__main__":
    pytest.main([__file__])
