-- 工站監控系統數據庫初始化腳本
-- 使用現有的AIoT數據庫

-- 使用數據庫
USE AIoT;

-- 創建工站日誌表
CREATE TABLE IF NOT EXISTS workstation_log (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主鍵，自動遞增',
    station VARCHAR(20) NOT NULL COMMENT '工站名稱（如 Station 12）',
    arrival_time DATETIME NULL COMMENT '停留（ON）發生時間',
    stay_duration INT NULL COMMENT '停留（ON）的持續時間（秒）',
    leave_time DATETIME NULL COMMENT '離開（OFF）發生時間', 
    off_duration INT NULL COMMENT '離開（OFF）的持續時間（秒）',
    end_time DATETIME NULL COMMENT '本次生產結束時間（第12站時記錄）',
    data_json JSON NULL COMMENT '完整數據的JSON格式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '記錄創建時間',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '記錄更新時間',
    
    INDEX idx_station (station),
    INDEX idx_arrival_time (arrival_time),
    INDEX idx_leave_time (leave_time),
    INDEX idx_end_time (end_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工站停留與離開時間記錄表';

-- 創建生產週期表（用於記錄完整的生產週期）
CREATE TABLE IF NOT EXISTS production_cycle (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主鍵，自動遞增',
    cycle_id VARCHAR(50) UNIQUE NOT NULL COMMENT '生產週期唯一標識',
    start_time DATETIME NOT NULL COMMENT '生產開始時間',
    end_time DATETIME NULL COMMENT '生產結束時間',
    total_duration INT NULL COMMENT '總生產時間（秒）',
    status ENUM('RUNNING', 'COMPLETED', 'ABORTED') DEFAULT 'RUNNING' COMMENT '生產狀態',
    stations_data JSON NULL COMMENT '所有工站數據的JSON格式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '記錄創建時間',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '記錄更新時間',
    
    INDEX idx_cycle_id (cycle_id),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生產週期記錄表';

-- 創建系統狀態表（用於記錄系統運行狀態）
CREATE TABLE IF NOT EXISTS system_status (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主鍵，自動遞增',
    component VARCHAR(50) NOT NULL COMMENT '組件名稱（PLC, DATABASE, MONITOR等）',
    status ENUM('ONLINE', 'OFFLINE', 'ERROR') NOT NULL COMMENT '狀態',
    last_update DATETIME NOT NULL COMMENT '最後更新時間',
    error_message TEXT NULL COMMENT '錯誤信息',
    metadata JSON NULL COMMENT '額外的狀態信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '記錄創建時間',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '記錄更新時間',
    
    UNIQUE KEY uk_component (component),
    INDEX idx_status (status),
    INDEX idx_last_update (last_update)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系統狀態記錄表';

-- 插入初始系統狀態
INSERT INTO system_status (component, status, last_update) VALUES
('PLC', 'OFFLINE', NOW()),
('DATABASE', 'ONLINE', NOW()),
('MONITOR', 'OFFLINE', NOW())
ON DUPLICATE KEY UPDATE 
    status = VALUES(status),
    last_update = VALUES(last_update);

-- 創建視圖：最新工站狀態
CREATE OR REPLACE VIEW v_latest_station_status AS
SELECT 
    station,
    MAX(arrival_time) as last_arrival,
    MAX(leave_time) as last_leave,
    CASE 
        WHEN MAX(arrival_time) > MAX(leave_time) THEN 'OCCUPIED'
        WHEN MAX(leave_time) > MAX(arrival_time) THEN 'EMPTY'
        ELSE 'UNKNOWN'
    END as current_status
FROM workstation_log 
WHERE arrival_time IS NOT NULL OR leave_time IS NOT NULL
GROUP BY station;

-- 創建視圖：生產效率統計
CREATE OR REPLACE VIEW v_production_efficiency AS
SELECT 
    DATE(created_at) as production_date,
    COUNT(DISTINCT cycle_id) as total_cycles,
    AVG(total_duration) as avg_cycle_time,
    MIN(total_duration) as min_cycle_time,
    MAX(total_duration) as max_cycle_time
FROM production_cycle 
WHERE status = 'COMPLETED'
GROUP BY DATE(created_at);

COMMIT;
