"""
配置管理器
負責加載和管理系統配置
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger


class ConfigManager:
    """配置管理器類"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默認為 config/config.yaml
        """
        if config_path is None:
            # 獲取項目根目錄
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "config.yaml"
        
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> bool:
        """
        加載配置文件
        
        Returns:
            加載是否成功
        """
        try:
            if not self.config_path.exists():
                logger.error(f"配置文件不存在: {self.config_path}")
                return False
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
            
            logger.info(f"配置文件加載成功: {self.config_path}")
            return True
            
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式錯誤: {e}")
            return False
        except Exception as e:
            logger.error(f"加載配置文件失敗: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        獲取配置值（支持點號分隔的嵌套鍵）
        
        Args:
            key: 配置鍵，支持 'section.subsection.key' 格式
            default: 默認值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        獲取配置段
        
        Args:
            section: 段名稱
            
        Returns:
            配置段字典
        """
        return self.config.get(section, {})
    
    def set(self, key: str, value: Any):
        """
        設置配置值
        
        Args:
            key: 配置鍵
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 導航到最後一級
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 設置值
        config[keys[-1]] = value
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            保存是否成功
        """
        try:
            # 確保目錄存在
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置文件保存成功: {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失敗: {e}")
            return False
    
    def validate_config(self) -> bool:
        """
        驗證配置完整性
        
        Returns:
            驗證是否通過
        """
        required_sections = ['system', 'plc', 'database', 'monitoring', 'logging']
        
        for section in required_sections:
            if section not in self.config:
                logger.error(f"缺少必需的配置段: {section}")
                return False
        
        # 驗證PLC配置
        plc_config = self.config.get('plc', {})
        if not plc_config.get('ip') or not plc_config.get('port'):
            logger.error("PLC配置不完整，缺少IP或端口")
            return False
        
        # 驗證數據庫配置
        db_config = self.config.get('database', {})
        required_db_keys = ['host', 'port', 'username', 'password', 'database']
        for key in required_db_keys:
            if not db_config.get(key):
                logger.error(f"數據庫配置不完整，缺少: {key}")
                return False
        
        logger.info("配置驗證通過")
        return True
    
    def get_plc_config(self) -> Dict[str, Any]:
        """獲取PLC配置"""
        return self.get_section('plc')
    
    def get_database_config(self) -> Dict[str, Any]:
        """獲取數據庫配置"""
        return self.get_section('database')
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """獲取監控配置"""
        return self.get_section('monitoring')
    
    def get_logging_config(self) -> Dict[str, Any]:
        """獲取日誌配置"""
        return self.get_section('logging')
    
    def get_system_config(self) -> Dict[str, Any]:
        """獲取系統配置"""
        return self.get_section('system')
    
    def is_debug_mode(self) -> bool:
        """檢查是否為調試模式"""
        return self.get('system.debug', False)
    
    def get_log_level(self) -> str:
        """獲取日誌級別"""
        return self.get('system.log_level', 'INFO')
    
    def update_from_env(self):
        """從環境變量更新配置"""
        # PLC配置
        if os.getenv('PLC_IP'):
            self.set('plc.ip', os.getenv('PLC_IP'))
        if os.getenv('PLC_PORT'):
            self.set('plc.port', int(os.getenv('PLC_PORT')))
        
        # 數據庫配置
        if os.getenv('DB_HOST'):
            self.set('database.host', os.getenv('DB_HOST'))
        if os.getenv('DB_PORT'):
            self.set('database.port', int(os.getenv('DB_PORT')))
        if os.getenv('DB_USERNAME'):
            self.set('database.username', os.getenv('DB_USERNAME'))
        if os.getenv('DB_PASSWORD'):
            self.set('database.password', os.getenv('DB_PASSWORD'))
        if os.getenv('DB_DATABASE'):
            self.set('database.database', os.getenv('DB_DATABASE'))
        
        # 系統配置
        if os.getenv('DEBUG'):
            self.set('system.debug', os.getenv('DEBUG').lower() == 'true')
        if os.getenv('LOG_LEVEL'):
            self.set('system.log_level', os.getenv('LOG_LEVEL'))
        
        logger.info("配置已從環境變量更新")
    
    def __str__(self) -> str:
        """返回配置的字符串表示（隱藏敏感信息）"""
        safe_config = self.config.copy()
        
        # 隱藏敏感信息
        if 'database' in safe_config:
            if 'password' in safe_config['database']:
                safe_config['database']['password'] = '***'
        
        return yaml.dump(safe_config, default_flow_style=False, allow_unicode=True)


# 全局配置管理器實例
_config_manager: Optional[ConfigManager] = None


def get_config_manager(config_path: Optional[str] = None) -> ConfigManager:
    """
    獲取全局配置管理器實例
    
    Args:
        config_path: 配置文件路径（僅在首次調用時有效）
        
    Returns:
        配置管理器實例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_path)
    return _config_manager


def get_config(key: str, default: Any = None) -> Any:
    """
    獲取配置值的便捷函數
    
    Args:
        key: 配置鍵
        default: 默認值
        
    Returns:
        配置值
    """
    return get_config_manager().get(key, default)
