"""
Modbus TCP客戶端
負責與匯川PLC進行Modbus TCP通訊
"""

import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pymodbus.client import ModbusTcpClient
from pymodbus.exceptions import ModbusException, ConnectionException
from loguru import logger


@dataclass
class PLCRegister:
    """PLC寄存器定義"""
    address: int
    name: str
    description: str
    data_type: str = "bool"  # bool, int16, int32, float


@dataclass
class PLCReadResult:
    """PLC讀取結果"""
    success: bool
    timestamp: float
    data: Dict[str, Any]
    error_message: Optional[str] = None


class ModbusTCPClient:
    """Modbus TCP客戶端類"""
    
    def __init__(self, host: str, port: int = 502, timeout: int = 5, retry_count: int = 3):
        """
        初始化Modbus TCP客戶端
        
        Args:
            host: PLC IP地址
            port: Modbus TCP端口
            timeout: 連接超時時間
            retry_count: 重試次數
        """
        self.host = host
        self.port = port
        self.timeout = timeout
        self.retry_count = retry_count
        self.client = None
        self.is_connected = False
        self.last_error = None
        
        # 定義寄存器映射
        self.registers = {
            'stay_trigger': PLCRegister(100, 'M100', '停留觸發寄存器'),
            'leave_trigger': PLCRegister(101, 'M101', '離開觸發寄存器'),
        }
        
        logger.info(f"Modbus TCP客戶端初始化: {host}:{port}")
    
    def connect(self) -> bool:
        """連接到PLC"""
        try:
            self.client = ModbusTcpClient(
                host=self.host,
                port=self.port,
                timeout=self.timeout
            )
            
            if self.client.connect():
                self.is_connected = True
                self.last_error = None
                logger.info(f"成功連接到PLC: {self.host}:{self.port}")
                return True
            else:
                self.is_connected = False
                self.last_error = "連接失敗"
                logger.error(f"無法連接到PLC: {self.host}:{self.port}")
                return False
                
        except Exception as e:
            self.is_connected = False
            self.last_error = str(e)
            logger.error(f"PLC連接異常: {e}")
            return False
    
    def disconnect(self):
        """斷開PLC連接"""
        if self.client:
            self.client.close()
            self.is_connected = False
            logger.info("PLC連接已斷開")
    
    def reconnect(self) -> bool:
        """重新連接PLC"""
        logger.info("嘗試重新連接PLC...")
        self.disconnect()
        time.sleep(1)
        return self.connect()
    
    def read_coils(self, address: int, count: int = 1) -> Optional[List[bool]]:
        """
        讀取線圈狀態
        
        Args:
            address: 起始地址
            count: 讀取數量
            
        Returns:
            線圈狀態列表，失敗返回None
        """
        if not self.is_connected:
            logger.warning("PLC未連接，無法讀取線圈")
            return None
        
        try:
            response = self.client.read_coils(address, count)
            if response.isError():
                logger.error(f"讀取線圈失敗: {response}")
                return None
            
            return response.bits[:count]
            
        except Exception as e:
            logger.error(f"讀取線圈異常: {e}")
            self.last_error = str(e)
            return None
    
    def read_discrete_inputs(self, address: int, count: int = 1) -> Optional[List[bool]]:
        """
        讀取離散輸入
        
        Args:
            address: 起始地址
            count: 讀取數量
            
        Returns:
            離散輸入狀態列表，失敗返回None
        """
        if not self.is_connected:
            logger.warning("PLC未連接，無法讀取離散輸入")
            return None
        
        try:
            response = self.client.read_discrete_inputs(address, count)
            if response.isError():
                logger.error(f"讀取離散輸入失敗: {response}")
                return None
            
            return response.bits[:count]
            
        except Exception as e:
            logger.error(f"讀取離散輸入異常: {e}")
            self.last_error = str(e)
            return None
    
    def read_holding_registers(self, address: int, count: int = 1) -> Optional[List[int]]:
        """
        讀取保持寄存器
        
        Args:
            address: 起始地址
            count: 讀取數量
            
        Returns:
            寄存器值列表，失敗返回None
        """
        if not self.is_connected:
            logger.warning("PLC未連接，無法讀取保持寄存器")
            return None
        
        try:
            response = self.client.read_holding_registers(address, count)
            if response.isError():
                logger.error(f"讀取保持寄存器失敗: {response}")
                return None
            
            return response.registers
            
        except Exception as e:
            logger.error(f"讀取保持寄存器異常: {e}")
            self.last_error = str(e)
            return None
    
    def read_station_triggers(self) -> PLCReadResult:
        """
        讀取工站觸發狀態（M100和M101）
        
        Returns:
            PLC讀取結果
        """
        timestamp = time.time()
        
        if not self.is_connected:
            return PLCReadResult(
                success=False,
                timestamp=timestamp,
                data={},
                error_message="PLC未連接"
            )
        
        try:
            # 讀取M100和M101（線圈地址）
            stay_trigger = self.read_coils(100, 1)  # M100
            leave_trigger = self.read_coils(101, 1)  # M101
            
            if stay_trigger is None or leave_trigger is None:
                return PLCReadResult(
                    success=False,
                    timestamp=timestamp,
                    data={},
                    error_message="讀取觸發寄存器失敗"
                )
            
            data = {
                'stay_trigger': stay_trigger[0] if stay_trigger else False,
                'leave_trigger': leave_trigger[0] if leave_trigger else False,
            }
            
            return PLCReadResult(
                success=True,
                timestamp=timestamp,
                data=data
            )
            
        except Exception as e:
            logger.error(f"讀取工站觸發狀態異常: {e}")
            return PLCReadResult(
                success=False,
                timestamp=timestamp,
                data={},
                error_message=str(e)
            )
    
    def read_with_retry(self, read_func, *args, **kwargs) -> Any:
        """
        帶重試的讀取操作
        
        Args:
            read_func: 讀取函數
            *args: 函數參數
            **kwargs: 函數關鍵字參數
            
        Returns:
            讀取結果
        """
        for attempt in range(self.retry_count):
            try:
                if not self.is_connected:
                    if not self.reconnect():
                        continue
                
                result = read_func(*args, **kwargs)
                if result is not None:
                    return result
                    
            except (ConnectionException, ModbusException) as e:
                logger.warning(f"讀取失敗，嘗試重連 (第{attempt + 1}次): {e}")
                self.is_connected = False
                if attempt < self.retry_count - 1:
                    time.sleep(1)
                    self.reconnect()
            except Exception as e:
                logger.error(f"讀取異常: {e}")
                break
        
        logger.error(f"讀取失敗，已重試{self.retry_count}次")
        return None
    
    def get_connection_status(self) -> Dict[str, Any]:
        """獲取連接狀態信息"""
        return {
            'connected': self.is_connected,
            'host': self.host,
            'port': self.port,
            'last_error': self.last_error,
            'timeout': self.timeout,
            'retry_count': self.retry_count
        }
