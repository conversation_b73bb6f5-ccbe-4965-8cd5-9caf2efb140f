"""
PLC管理器
負責PLC連接管理和數據採集的高級封裝
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime
from loguru import logger

from .modbus_client import ModbusTCPClient, PLCReadResult


@dataclass
class StationStatus:
    """工站狀態"""
    station_id: int
    station_name: str
    is_occupied: bool
    last_change_time: datetime
    stay_trigger: bool
    leave_trigger: bool


class PLCManager:
    """PLC管理器類"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化PLC管理器
        
        Args:
            config: PLC配置字典
        """
        self.config = config
        self.client = ModbusTCPClient(
            host=config['ip'],
            port=config['port'],
            timeout=config.get('timeout', 5),
            retry_count=config.get('retry_count', 3)
        )
        
        self.scan_interval = config.get('scan_interval', 1.0)
        self.total_stations = config.get('stations', {}).get('total_count', 12)
        
        # 狀態管理
        self.is_running = False
        self.scan_thread = None
        self.last_scan_time = None
        self.scan_count = 0
        self.error_count = 0
        
        # 工站狀態
        self.station_states = {}
        self.previous_triggers = {'stay': False, 'leave': False}
        
        # 回調函數
        self.callbacks = {
            'on_stay_trigger': [],
            'on_leave_trigger': [],
            'on_connection_change': [],
            'on_error': []
        }
        
        # 線程鎖
        self.lock = threading.Lock()
        
        logger.info(f"PLC管理器初始化完成: {config['ip']}:{config['port']}")
    
    def add_callback(self, event_type: str, callback: Callable):
        """
        添加事件回調函數
        
        Args:
            event_type: 事件類型 ('on_stay_trigger', 'on_leave_trigger', 'on_connection_change', 'on_error')
            callback: 回調函數
        """
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            logger.info(f"添加回調函數: {event_type}")
        else:
            logger.warning(f"未知的事件類型: {event_type}")
    
    def remove_callback(self, event_type: str, callback: Callable):
        """移除事件回調函數"""
        if event_type in self.callbacks and callback in self.callbacks[event_type]:
            self.callbacks[event_type].remove(callback)
            logger.info(f"移除回調函數: {event_type}")
    
    def _trigger_callbacks(self, event_type: str, *args, **kwargs):
        """觸發回調函數"""
        for callback in self.callbacks.get(event_type, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"回調函數執行錯誤 ({event_type}): {e}")
    
    def connect(self) -> bool:
        """連接到PLC"""
        success = self.client.connect()
        if success:
            self._trigger_callbacks('on_connection_change', True, "PLC連接成功")
        else:
            self._trigger_callbacks('on_connection_change', False, "PLC連接失敗")
            self._trigger_callbacks('on_error', "PLC連接失敗", self.client.last_error)
        return success
    
    def disconnect(self):
        """斷開PLC連接"""
        self.client.disconnect()
        self._trigger_callbacks('on_connection_change', False, "PLC連接已斷開")
    
    def start_monitoring(self):
        """開始監控"""
        if self.is_running:
            logger.warning("監控已在運行中")
            return
        
        if not self.client.is_connected:
            if not self.connect():
                logger.error("無法連接PLC，監控啟動失敗")
                return
        
        self.is_running = True
        self.scan_thread = threading.Thread(target=self._scan_loop, daemon=True)
        self.scan_thread.start()
        logger.info("PLC監控已啟動")
    
    def stop_monitoring(self):
        """停止監控"""
        self.is_running = False
        if self.scan_thread and self.scan_thread.is_alive():
            self.scan_thread.join(timeout=5)
        logger.info("PLC監控已停止")
    
    def _scan_loop(self):
        """掃描循環"""
        logger.info("PLC掃描循環開始")
        
        while self.is_running:
            try:
                start_time = time.time()
                
                # 讀取PLC數據
                result = self.client.read_station_triggers()
                
                with self.lock:
                    self.last_scan_time = datetime.now()
                    self.scan_count += 1
                
                if result.success:
                    self._process_scan_result(result)
                else:
                    self.error_count += 1
                    logger.error(f"PLC掃描失敗: {result.error_message}")
                    self._trigger_callbacks('on_error', "PLC掃描失敗", result.error_message)
                
                # 計算睡眠時間
                elapsed = time.time() - start_time
                sleep_time = max(0, self.scan_interval - elapsed)
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
                
            except Exception as e:
                self.error_count += 1
                logger.error(f"掃描循環異常: {e}")
                self._trigger_callbacks('on_error', "掃描循環異常", str(e))
                time.sleep(1)  # 異常時短暫休息
        
        logger.info("PLC掃描循環結束")
    
    def _process_scan_result(self, result: PLCReadResult):
        """
        處理掃描結果
        
        Args:
            result: PLC讀取結果
        """
        current_stay = result.data.get('stay_trigger', False)
        current_leave = result.data.get('leave_trigger', False)
        
        # 檢測停留觸發（上升沿）
        if current_stay and not self.previous_triggers['stay']:
            logger.info("檢測到停留觸發 (M100 ON)")
            self._trigger_callbacks('on_stay_trigger', result.timestamp)
        
        # 檢測離開觸發（上升沿）
        if current_leave and not self.previous_triggers['leave']:
            logger.info("檢測到離開觸發 (M101 ON)")
            self._trigger_callbacks('on_leave_trigger', result.timestamp)
        
        # 更新前一次狀態
        self.previous_triggers['stay'] = current_stay
        self.previous_triggers['leave'] = current_leave
    
    def get_status(self) -> Dict[str, Any]:
        """獲取PLC管理器狀態"""
        with self.lock:
            return {
                'is_running': self.is_running,
                'is_connected': self.client.is_connected,
                'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
                'scan_count': self.scan_count,
                'error_count': self.error_count,
                'scan_interval': self.scan_interval,
                'total_stations': self.total_stations,
                'connection_info': self.client.get_connection_status(),
                'current_triggers': self.previous_triggers.copy()
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計信息"""
        with self.lock:
            uptime = (datetime.now() - self.last_scan_time).total_seconds() if self.last_scan_time else 0
            success_rate = ((self.scan_count - self.error_count) / self.scan_count * 100) if self.scan_count > 0 else 0
            
            return {
                'uptime_seconds': uptime,
                'total_scans': self.scan_count,
                'error_count': self.error_count,
                'success_rate': round(success_rate, 2),
                'average_scan_interval': self.scan_interval,
                'callbacks_registered': {
                    event: len(callbacks) for event, callbacks in self.callbacks.items()
                }
            }
    
    def manual_read(self) -> PLCReadResult:
        """手動讀取PLC數據"""
        if not self.client.is_connected:
            return PLCReadResult(
                success=False,
                timestamp=time.time(),
                data={},
                error_message="PLC未連接"
            )
        
        return self.client.read_station_triggers()
    
    def reset_statistics(self):
        """重置統計信息"""
        with self.lock:
            self.scan_count = 0
            self.error_count = 0
            self.last_scan_time = None
        logger.info("統計信息已重置")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_monitoring()
        self.disconnect()
