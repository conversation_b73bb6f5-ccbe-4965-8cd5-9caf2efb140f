"""
數據管理器
負責工站監控數據的存儲、查詢和管理
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from loguru import logger

from .connection import get_db_session, DatabaseManager
from .models import WorkstationLog, ProductionCycle, SystemStatus, ProductionStatus, SystemComponentStatus
from ..monitoring.station_monitor import StationRecord, StationEvent
from ..monitoring.production_tracker import ProductionCycleData


class DataManager:
    """數據管理器類"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化數據管理器
        
        Args:
            db_manager: 數據庫管理器實例
        """
        self.db_manager = db_manager
        logger.info("數據管理器初始化完成")
    
    def save_station_record(self, record: StationRecord) -> bool:
        """
        保存工站記錄
        
        Args:
            record: 工站記錄
            
        Returns:
            保存是否成功
        """
        try:
            with get_db_session() as session:
                # 檢查是否已存在記錄
                existing = session.query(WorkstationLog).filter(
                    and_(
                        WorkstationLog.station == record.station_name,
                        WorkstationLog.arrival_time == record.arrival_time
                    )
                ).first()
                
                if existing:
                    # 更新現有記錄
                    existing.stay_duration = record.stay_duration
                    existing.leave_time = record.leave_time
                    existing.off_duration = record.off_duration
                    existing.end_time = record.end_time
                    existing.data_json = record.to_json()
                    logger.debug(f"更新工站記錄: {record.station_name}")
                else:
                    # 創建新記錄
                    log_entry = WorkstationLog(
                        station=record.station_name,
                        arrival_time=record.arrival_time,
                        stay_duration=record.stay_duration,
                        leave_time=record.leave_time,
                        off_duration=record.off_duration,
                        end_time=record.end_time,
                        data_json=record.to_json()
                    )
                    session.add(log_entry)
                    logger.debug(f"創建工站記錄: {record.station_name}")
                
                session.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存工站記錄失敗: {e}")
            return False
    
    def save_production_cycle(self, cycle_data: ProductionCycleData) -> bool:
        """
        保存生產週期數據
        
        Args:
            cycle_data: 生產週期數據
            
        Returns:
            保存是否成功
        """
        try:
            with get_db_session() as session:
                # 檢查是否已存在記錄
                existing = session.query(ProductionCycle).filter(
                    ProductionCycle.cycle_id == cycle_data.cycle_id
                ).first()
                
                if existing:
                    # 更新現有記錄
                    existing.end_time = cycle_data.end_time
                    existing.total_duration = cycle_data.total_duration
                    existing.status = ProductionStatus(cycle_data.status)
                    existing.stations_data = cycle_data.stations_data
                    logger.debug(f"更新生產週期: {cycle_data.cycle_id}")
                else:
                    # 創建新記錄
                    cycle_entry = ProductionCycle(
                        cycle_id=cycle_data.cycle_id,
                        start_time=cycle_data.start_time,
                        end_time=cycle_data.end_time,
                        total_duration=cycle_data.total_duration,
                        status=ProductionStatus(cycle_data.status),
                        stations_data=cycle_data.stations_data
                    )
                    session.add(cycle_entry)
                    logger.debug(f"創建生產週期: {cycle_data.cycle_id}")
                
                session.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存生產週期失敗: {e}")
            return False
    
    def update_system_status(self, component: str, status: str, error_message: Optional[str] = None, extra_data: Optional[Dict] = None) -> bool:
        """
        更新系統狀態
        
        Args:
            component: 組件名稱
            status: 狀態
            error_message: 錯誤信息
            extra_data: 額外元數據
            
        Returns:
            更新是否成功
        """
        try:
            with get_db_session() as session:
                # 查找或創建系統狀態記錄
                system_status = session.query(SystemStatus).filter(
                    SystemStatus.component == component
                ).first()
                
                if system_status:
                    system_status.status = SystemComponentStatus(status)
                    system_status.last_update = datetime.now()
                    system_status.error_message = error_message
                    system_status.extra_data = extra_data
                else:
                    system_status = SystemStatus(
                        component=component,
                        status=SystemComponentStatus(status),
                        last_update=datetime.now(),
                        error_message=error_message,
                        extra_data=extra_data
                    )
                    session.add(system_status)
                
                session.commit()
                logger.debug(f"更新系統狀態: {component} -> {status}")
                return True
                
        except Exception as e:
            logger.error(f"更新系統狀態失敗: {e}")
            return False
    
    def get_recent_station_logs(self, hours: int = 24, station: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        獲取最近的工站日誌
        
        Args:
            hours: 查詢最近多少小時的數據
            station: 特定工站名稱（可選）
            
        Returns:
            工站日誌列表
        """
        try:
            with get_db_session() as session:
                query = session.query(WorkstationLog)
                
                # 時間過濾
                since_time = datetime.now() - timedelta(hours=hours)
                query = query.filter(WorkstationLog.created_at >= since_time)
                
                # 工站過濾
                if station:
                    query = query.filter(WorkstationLog.station == station)
                
                # 按時間倒序
                query = query.order_by(desc(WorkstationLog.created_at))
                
                logs = query.all()
                return [log.to_dict() for log in logs]
                
        except Exception as e:
            logger.error(f"查詢工站日誌失敗: {e}")
            return []
    
    def get_production_cycles(self, days: int = 7, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        獲取生產週期記錄
        
        Args:
            days: 查詢最近多少天的數據
            status: 週期狀態過濾（可選）
            
        Returns:
            生產週期列表
        """
        try:
            with get_db_session() as session:
                query = session.query(ProductionCycle)
                
                # 時間過濾
                since_time = datetime.now() - timedelta(days=days)
                query = query.filter(ProductionCycle.created_at >= since_time)
                
                # 狀態過濾
                if status:
                    query = query.filter(ProductionCycle.status == ProductionStatus(status))
                
                # 按時間倒序
                query = query.order_by(desc(ProductionCycle.start_time))
                
                cycles = query.all()
                return [cycle.to_dict() for cycle in cycles]
                
        except Exception as e:
            logger.error(f"查詢生產週期失敗: {e}")
            return []
    
    def get_system_status_all(self) -> List[Dict[str, Any]]:
        """獲取所有系統狀態"""
        try:
            with get_db_session() as session:
                statuses = session.query(SystemStatus).all()
                return [status.to_dict() for status in statuses]
                
        except Exception as e:
            logger.error(f"查詢系統狀態失敗: {e}")
            return []
    
    def get_production_statistics(self, days: int = 30) -> Dict[str, Any]:
        """
        獲取生產統計信息
        
        Args:
            days: 統計天數
            
        Returns:
            統計信息字典
        """
        try:
            with get_db_session() as session:
                since_time = datetime.now() - timedelta(days=days)
                
                # 基本統計
                total_cycles = session.query(ProductionCycle).filter(
                    ProductionCycle.created_at >= since_time
                ).count()
                
                completed_cycles = session.query(ProductionCycle).filter(
                    and_(
                        ProductionCycle.created_at >= since_time,
                        ProductionCycle.status == ProductionStatus.COMPLETED
                    )
                ).count()
                
                # 平均週期時間
                avg_duration = session.query(func.avg(ProductionCycle.total_duration)).filter(
                    and_(
                        ProductionCycle.created_at >= since_time,
                        ProductionCycle.status == ProductionStatus.COMPLETED
                    )
                ).scalar()
                
                # 最短和最長週期時間
                min_duration = session.query(func.min(ProductionCycle.total_duration)).filter(
                    and_(
                        ProductionCycle.created_at >= since_time,
                        ProductionCycle.status == ProductionStatus.COMPLETED
                    )
                ).scalar()
                
                max_duration = session.query(func.max(ProductionCycle.total_duration)).filter(
                    and_(
                        ProductionCycle.created_at >= since_time,
                        ProductionCycle.status == ProductionStatus.COMPLETED
                    )
                ).scalar()
                
                # 每日統計
                daily_stats = session.query(
                    func.date(ProductionCycle.start_time).label('date'),
                    func.count(ProductionCycle.id).label('total'),
                    func.sum(func.case([(ProductionCycle.status == ProductionStatus.COMPLETED, 1)], else_=0)).label('completed')
                ).filter(
                    ProductionCycle.created_at >= since_time
                ).group_by(
                    func.date(ProductionCycle.start_time)
                ).all()
                
                return {
                    'period_days': days,
                    'total_cycles': total_cycles,
                    'completed_cycles': completed_cycles,
                    'completion_rate': (completed_cycles / total_cycles * 100) if total_cycles > 0 else 0,
                    'average_duration': float(avg_duration) if avg_duration else 0,
                    'min_duration': float(min_duration) if min_duration else 0,
                    'max_duration': float(max_duration) if max_duration else 0,
                    'daily_statistics': [
                        {
                            'date': stat.date.isoformat(),
                            'total_cycles': stat.total,
                            'completed_cycles': stat.completed,
                            'completion_rate': (stat.completed / stat.total * 100) if stat.total > 0 else 0
                        }
                        for stat in daily_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"獲取生產統計失敗: {e}")
            return {}
    
    def cleanup_old_data(self, days: int = 90) -> bool:
        """
        清理舊數據
        
        Args:
            days: 保留最近多少天的數據
            
        Returns:
            清理是否成功
        """
        try:
            with get_db_session() as session:
                cutoff_time = datetime.now() - timedelta(days=days)
                
                # 清理舊的工站日誌
                deleted_logs = session.query(WorkstationLog).filter(
                    WorkstationLog.created_at < cutoff_time
                ).delete()
                
                # 清理舊的生產週期（保留已完成的）
                deleted_cycles = session.query(ProductionCycle).filter(
                    and_(
                        ProductionCycle.created_at < cutoff_time,
                        ProductionCycle.status != ProductionStatus.COMPLETED
                    )
                ).delete()
                
                session.commit()
                logger.info(f"清理舊數據完成: 工站日誌 {deleted_logs} 條, 生產週期 {deleted_cycles} 條")
                return True
                
        except Exception as e:
            logger.error(f"清理舊數據失敗: {e}")
            return False
