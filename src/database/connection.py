"""
數據庫連接管理
負責數據庫連接的建立、管理和關閉
"""

import os
from typing import Optional, Dict, Any
from contextlib import contextmanager
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from loguru import logger
import pymysql

from .models import Base


class DatabaseManager:
    """數據庫管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化數據庫管理器
        
        Args:
            config: 數據庫配置字典
        """
        self.config = config
        self.engine = None
        self.SessionLocal = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化數據庫引擎"""
        try:
            # 構建數據庫連接URL
            db_url = self._build_database_url()
            
            # 創建引擎
            self.engine = create_engine(
                db_url,
                poolclass=QueuePool,
                pool_size=self.config.get('pool_size', 10),
                max_overflow=self.config.get('max_overflow', 20),
                pool_timeout=self.config.get('pool_timeout', 30),
                pool_recycle=self.config.get('pool_recycle', 3600),
                echo=self.config.get('debug', False),
                connect_args={
                    "charset": self.config.get('charset', 'utf8mb4'),
                    "autocommit": False
                }
            )
            
            # 創建會話工廠
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info("數據庫引擎初始化成功")
            
        except Exception as e:
            logger.error(f"數據庫引擎初始化失敗: {e}")
            raise
    
    def _build_database_url(self) -> str:
        """構建數據庫連接URL"""
        return (
            f"mysql+pymysql://"
            f"{self.config['username']}:{self.config['password']}"
            f"@{self.config['host']}:{self.config['port']}"
            f"/{self.config['database']}"
        )
    
    def test_connection(self) -> bool:
        """測試數據庫連接"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("數據庫連接測試成功")
            return True
        except Exception as e:
            logger.error(f"數據庫連接測試失敗: {e}")
            return False
    
    def create_tables(self):
        """創建數據表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("數據表創建成功")
        except Exception as e:
            logger.error(f"數據表創建失敗: {e}")
            raise
    
    def drop_tables(self):
        """刪除數據表（謹慎使用）"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.warning("數據表已刪除")
        except Exception as e:
            logger.error(f"數據表刪除失敗: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """獲取數據庫會話（上下文管理器）"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"數據庫會話錯誤: {e}")
            raise
        finally:
            session.close()
    
    def get_session_direct(self) -> Session:
        """直接獲取數據庫會話（需要手動管理）"""
        return self.SessionLocal()
    
    def execute_sql_file(self, sql_file_path: str):
        """執行SQL文件"""
        try:
            if not os.path.exists(sql_file_path):
                raise FileNotFoundError(f"SQL文件不存在: {sql_file_path}")
            
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            # 分割SQL語句（簡單的分割，可能需要更複雜的邏輯）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            with self.engine.connect() as conn:
                for stmt in sql_statements:
                    if stmt:
                        conn.execute(text(stmt))
                        conn.commit()
            
            logger.info(f"SQL文件執行成功: {sql_file_path}")
            
        except Exception as e:
            logger.error(f"SQL文件執行失敗: {e}")
            raise
    
    def close(self):
        """關閉數據庫連接"""
        if self.engine:
            self.engine.dispose()
            logger.info("數據庫連接已關閉")


# 全局數據庫管理器實例
_db_manager: Optional[DatabaseManager] = None


def initialize_database(config: Dict[str, Any]) -> DatabaseManager:
    """初始化全局數據庫管理器"""
    global _db_manager
    _db_manager = DatabaseManager(config)
    return _db_manager


def get_database_manager() -> DatabaseManager:
    """獲取全局數據庫管理器"""
    if _db_manager is None:
        raise RuntimeError("數據庫管理器未初始化，請先調用 initialize_database()")
    return _db_manager


@contextmanager
def get_db_session():
    """獲取數據庫會話的便捷函數"""
    db_manager = get_database_manager()
    with db_manager.get_session() as session:
        yield session
