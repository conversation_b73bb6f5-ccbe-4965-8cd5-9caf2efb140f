#!/usr/bin/env python3
"""
JSON數據存儲管理器
負責將工站監控數據保存為JSON格式
"""

import json
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional
import threading
from loguru import logger


class JSONDataManager:
    """JSON數據存儲管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化JSON數據管理器
        
        Args:
            config: JSON存儲配置
        """
        self.config = config
        self.data_dir = Path(config.get('data_dir', 'data'))
        self.file_prefix = config.get('file_prefix', 'workstation_data')
        self.max_file_size = self._parse_size(config.get('max_file_size', '50MB'))
        self.backup_enabled = config.get('backup_enabled', True)
        self.backup_dir = Path(config.get('backup_dir', 'data/backup'))
        
        # 創建目錄
        self.data_dir.mkdir(parents=True, exist_ok=True)
        if self.backup_enabled:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 當前數據文件
        self.current_file = None
        self.current_file_size = 0
        
        # 線程鎖
        self._lock = threading.Lock()
        
        # 初始化當前文件
        self._initialize_current_file()
        
        logger.info(f"JSON數據管理器初始化完成，數據目錄: {self.data_dir}")
    
    def _parse_size(self, size_str: str) -> int:
        """解析文件大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _initialize_current_file(self):
        """初始化當前數據文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.file_prefix}_{timestamp}.json"
        self.current_file = self.data_dir / filename
        self.current_file_size = 0
        
        # 創建空的JSON文件
        initial_data = {
            "metadata": {
                "created_at": datetime.now(timezone.utc).isoformat(),
                "version": "1.0.0",
                "description": "工站監控數據"
            },
            "workstation_logs": []
        }
        
        with open(self.current_file, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, ensure_ascii=False, indent=2)
        
        self.current_file_size = self.current_file.stat().st_size
        logger.info(f"創建新的數據文件: {self.current_file}")
    
    def _rotate_file_if_needed(self):
        """如果文件過大則輪轉文件"""
        if self.current_file_size >= self.max_file_size:
            # 備份當前文件
            if self.backup_enabled:
                self._backup_current_file()
            
            # 創建新文件
            self._initialize_current_file()
    
    def _backup_current_file(self):
        """備份當前文件"""
        try:
            backup_filename = self.current_file.name
            backup_path = self.backup_dir / backup_filename
            
            # 複製文件到備份目錄
            import shutil
            shutil.copy2(self.current_file, backup_path)
            
            logger.info(f"文件已備份: {backup_path}")
        except Exception as e:
            logger.error(f"備份文件失敗: {e}")
    
    def save_workstation_log(self, log_data: Dict[str, Any]) -> bool:
        """
        保存工站日誌數據
        
        Args:
            log_data: 工站日誌數據
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                # 檢查是否需要輪轉文件
                self._rotate_file_if_needed()
                
                # 讀取現有數據
                with open(self.current_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 添加時間戳
                log_data['saved_at'] = datetime.now(timezone.utc).isoformat()
                
                # 添加新數據
                data['workstation_logs'].append(log_data)
                
                # 寫回文件
                with open(self.current_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                # 更新文件大小
                self.current_file_size = self.current_file.stat().st_size
                
                logger.debug(f"工站日誌已保存: 工站{log_data.get('station_id', 'unknown')}")
                return True
                
        except Exception as e:
            logger.error(f"保存工站日誌失敗: {e}")
            return False
    
    def save_batch_logs(self, logs: List[Dict[str, Any]]) -> bool:
        """
        批量保存工站日誌
        
        Args:
            logs: 工站日誌列表
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                # 檢查是否需要輪轉文件
                self._rotate_file_if_needed()
                
                # 讀取現有數據
                with open(self.current_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 添加時間戳到所有日誌
                current_time = datetime.now(timezone.utc).isoformat()
                for log in logs:
                    log['saved_at'] = current_time
                
                # 添加新數據
                data['workstation_logs'].extend(logs)
                
                # 寫回文件
                with open(self.current_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                # 更新文件大小
                self.current_file_size = self.current_file.stat().st_size
                
                logger.info(f"批量保存 {len(logs)} 條工站日誌")
                return True
                
        except Exception as e:
            logger.error(f"批量保存工站日誌失敗: {e}")
            return False
    
    def get_recent_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        獲取最近的日誌記錄
        
        Args:
            limit: 返回記錄數量限制
            
        Returns:
            List[Dict]: 日誌記錄列表
        """
        try:
            with self._lock:
                with open(self.current_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                logs = data.get('workstation_logs', [])
                return logs[-limit:] if len(logs) > limit else logs
                
        except Exception as e:
            logger.error(f"讀取最近日誌失敗: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取存儲統計信息
        
        Returns:
            Dict: 統計信息
        """
        try:
            stats = {
                "current_file": str(self.current_file),
                "current_file_size": self.current_file_size,
                "max_file_size": self.max_file_size,
                "data_directory": str(self.data_dir),
                "backup_enabled": self.backup_enabled,
                "total_files": len(list(self.data_dir.glob(f"{self.file_prefix}_*.json")))
            }
            
            # 獲取當前文件中的記錄數
            try:
                with open(self.current_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                stats["current_file_records"] = len(data.get('workstation_logs', []))
            except:
                stats["current_file_records"] = 0
            
            return stats
            
        except Exception as e:
            logger.error(f"獲取統計信息失敗: {e}")
            return {}
    
    def cleanup_old_files(self, keep_days: int = 30):
        """
        清理舊的數據文件
        
        Args:
            keep_days: 保留天數
        """
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            deleted_count = 0
            for file_path in self.data_dir.glob(f"{self.file_prefix}_*.json"):
                if file_path == self.current_file:
                    continue
                
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_date:
                    file_path.unlink()
                    deleted_count += 1
            
            logger.info(f"清理了 {deleted_count} 個舊數據文件")
            
        except Exception as e:
            logger.error(f"清理舊文件失敗: {e}")
