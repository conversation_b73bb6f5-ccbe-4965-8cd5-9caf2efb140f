"""
工站狀態監控器
負責監控工站的停留和離開狀態，計算時間並觸發相應事件
"""

import time
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from loguru import logger


class StationState(Enum):
    """工站狀態枚舉"""
    EMPTY = "EMPTY"          # 空閒
    OCCUPIED = "OCCUPIED"    # 有人停留
    UNKNOWN = "UNKNOWN"      # 未知狀態


@dataclass
class StationEvent:
    """工站事件"""
    station_id: int
    station_name: str
    event_type: str  # 'arrival', 'departure'
    timestamp: datetime
    duration: Optional[float] = None  # 持續時間（秒）
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StationRecord:
    """工站記錄"""
    station_id: int
    station_name: str
    arrival_time: Optional[datetime] = None
    stay_duration: Optional[float] = None
    leave_time: Optional[datetime] = None
    off_duration: Optional[float] = None
    end_time: Optional[datetime] = None  # 生產結束時間（僅第12站）
    
    def to_json(self) -> Dict[str, Any]:
        """轉換為JSON格式"""
        return {
            "station": self.station_name,
            "arrival_time": self.arrival_time.isoformat() if self.arrival_time else None,
            "stay_duration": self.stay_duration,
            "leave_time": self.leave_time.isoformat() if self.leave_time else None,
            "off_duration": self.off_duration,
            "end_time": self.end_time.isoformat() if self.end_time else None
        }


class StationMonitor:
    """工站狀態監控器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化工站監控器
        
        Args:
            config: 監控配置
        """
        self.config = config
        self.total_stations = config.get('stations', {}).get('total_count', 12)
        self.final_station = config.get('monitoring', {}).get('final_station', 12)
        self.debounce_time = config.get('monitoring', {}).get('debounce_time', 0.1)
        self.min_stay_time = config.get('monitoring', {}).get('min_stay_time', 1.0)
        self.max_stay_time = config.get('monitoring', {}).get('max_stay_time', 3600)
        
        # 工站狀態管理
        self.station_states: Dict[int, StationState] = {}
        self.station_records: Dict[int, StationRecord] = {}
        self.current_cycle_id: Optional[str] = None
        self.cycle_start_time: Optional[datetime] = None
        
        # 時間記錄
        self.last_stay_trigger_time: Optional[datetime] = None
        self.last_leave_trigger_time: Optional[datetime] = None
        self.debounce_stay_time: Optional[datetime] = None
        self.debounce_leave_time: Optional[datetime] = None
        
        # 回調函數
        self.callbacks = {
            'on_station_arrival': [],
            'on_station_departure': [],
            'on_production_start': [],
            'on_production_end': [],
            'on_cycle_complete': []
        }
        
        # 初始化工站狀態
        self._initialize_stations()
        
        logger.info(f"工站監控器初始化完成，監控{self.total_stations}個工站")
    
    def _initialize_stations(self):
        """初始化工站狀態"""
        for i in range(1, self.total_stations + 1):
            self.station_states[i] = StationState.EMPTY
            self.station_records[i] = StationRecord(
                station_id=i,
                station_name=f"Station {i}"
            )
    
    def add_callback(self, event_type: str, callback: Callable):
        """添加事件回調函數"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            logger.info(f"添加監控回調函數: {event_type}")
        else:
            logger.warning(f"未知的監控事件類型: {event_type}")
    
    def _trigger_callbacks(self, event_type: str, *args, **kwargs):
        """觸發回調函數"""
        for callback in self.callbacks.get(event_type, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"監控回調函數執行錯誤 ({event_type}): {e}")
    
    def on_stay_trigger(self, timestamp: float):
        """
        處理停留觸發事件
        
        Args:
            timestamp: 觸發時間戳
        """
        current_time = datetime.fromtimestamp(timestamp)
        
        # 防抖處理
        if (self.debounce_stay_time and 
            (current_time - self.debounce_stay_time).total_seconds() < self.debounce_time):
            return
        
        self.debounce_stay_time = current_time
        self.last_stay_trigger_time = current_time
        
        logger.info(f"處理停留觸發事件: {current_time}")
        
        # 開始新的生產週期（如果還沒有）
        if not self.current_cycle_id:
            self._start_new_cycle(current_time)
        
        # 處理工站到達事件
        self._process_station_arrival(current_time)
    
    def on_leave_trigger(self, timestamp: float):
        """
        處理離開觸發事件
        
        Args:
            timestamp: 觸發時間戳
        """
        current_time = datetime.fromtimestamp(timestamp)
        
        # 防抖處理
        if (self.debounce_leave_time and 
            (current_time - self.debounce_leave_time).total_seconds() < self.debounce_time):
            return
        
        self.debounce_leave_time = current_time
        self.last_leave_trigger_time = current_time
        
        logger.info(f"處理離開觸發事件: {current_time}")
        
        # 處理工站離開事件
        self._process_station_departure(current_time)
    
    def _start_new_cycle(self, start_time: datetime):
        """開始新的生產週期"""
        self.current_cycle_id = str(uuid.uuid4())
        self.cycle_start_time = start_time
        
        # 重置所有工站記錄
        self._initialize_stations()
        
        logger.info(f"開始新的生產週期: {self.current_cycle_id}")
        self._trigger_callbacks('on_production_start', self.current_cycle_id, start_time)
    
    def _process_station_arrival(self, arrival_time: datetime):
        """
        處理工站到達事件
        
        Args:
            arrival_time: 到達時間
        """
        # 簡化邏輯：假設按順序經過工站
        # 實際應用中可能需要更複雜的邏輯來確定具體是哪個工站
        
        # 找到第一個空閒的工站
        target_station = None
        for station_id in range(1, self.total_stations + 1):
            if self.station_states[station_id] == StationState.EMPTY:
                target_station = station_id
                break
        
        if target_station is None:
            # 如果所有工站都被佔用，使用第一個工站（可能是重新開始）
            target_station = 1
            logger.warning("所有工站都被佔用，重置到第一個工站")
        
        # 更新工站狀態
        self.station_states[target_station] = StationState.OCCUPIED
        record = self.station_records[target_station]
        record.arrival_time = arrival_time
        
        # 創建到達事件
        event = StationEvent(
            station_id=target_station,
            station_name=record.station_name,
            event_type='arrival',
            timestamp=arrival_time
        )
        
        logger.info(f"工站到達: {record.station_name} at {arrival_time}")
        self._trigger_callbacks('on_station_arrival', event, record)
    
    def _process_station_departure(self, departure_time: datetime):
        """
        處理工站離開事件
        
        Args:
            departure_time: 離開時間
        """
        # 找到最近到達的被佔用工站
        target_station = None
        latest_arrival = None
        
        for station_id, state in self.station_states.items():
            if state == StationState.OCCUPIED:
                record = self.station_records[station_id]
                if record.arrival_time and (latest_arrival is None or record.arrival_time > latest_arrival):
                    latest_arrival = record.arrival_time
                    target_station = station_id
        
        if target_station is None:
            logger.warning("沒有找到被佔用的工站進行離開處理")
            return
        
        # 更新工站狀態
        self.station_states[target_station] = StationState.EMPTY
        record = self.station_records[target_station]
        record.leave_time = departure_time
        
        # 計算停留時間
        if record.arrival_time:
            record.stay_duration = (departure_time - record.arrival_time).total_seconds()
            
            # 驗證停留時間
            if record.stay_duration < self.min_stay_time:
                logger.warning(f"停留時間過短: {record.stay_duration}秒 < {self.min_stay_time}秒")
            elif record.stay_duration > self.max_stay_time:
                logger.warning(f"停留時間過長: {record.stay_duration}秒 > {self.max_stay_time}秒")
        
        # 創建離開事件
        event = StationEvent(
            station_id=target_station,
            station_name=record.station_name,
            event_type='departure',
            timestamp=departure_time,
            duration=record.stay_duration
        )
        
        logger.info(f"工站離開: {record.station_name} at {departure_time}, 停留時間: {record.stay_duration}秒")
        self._trigger_callbacks('on_station_departure', event, record)
        
        # 檢查是否是最後一站
        if target_station == self.final_station:
            self._process_production_end(departure_time)
    
    def _process_production_end(self, end_time: datetime):
        """
        處理生產結束事件
        
        Args:
            end_time: 結束時間
        """
        if not self.current_cycle_id or not self.cycle_start_time:
            logger.warning("沒有活動的生產週期")
            return
        
        # 設置結束時間
        final_record = self.station_records[self.final_station]
        final_record.end_time = end_time
        
        # 計算總生產時間
        total_duration = (end_time - self.cycle_start_time).total_seconds()
        
        logger.info(f"生產週期結束: {self.current_cycle_id}, 總時間: {total_duration}秒")
        
        # 觸發生產結束回調
        self._trigger_callbacks('on_production_end', self.current_cycle_id, end_time, total_duration)
        
        # 觸發週期完成回調
        cycle_data = self.get_current_cycle_data()
        self._trigger_callbacks('on_cycle_complete', self.current_cycle_id, cycle_data)
        
        # 重置週期
        self.current_cycle_id = None
        self.cycle_start_time = None
    
    def get_current_cycle_data(self) -> Dict[str, Any]:
        """獲取當前週期數據"""
        stations_data = []
        for record in self.station_records.values():
            if record.arrival_time or record.leave_time:
                stations_data.append(record.to_json())
        
        return {
            'cycle_id': self.current_cycle_id,
            'start_time': self.cycle_start_time.isoformat() if self.cycle_start_time else None,
            'stations': stations_data,
            'total_stations': self.total_stations
        }
    
    def get_station_status(self) -> Dict[int, Dict[str, Any]]:
        """獲取所有工站狀態"""
        status = {}
        for station_id, state in self.station_states.items():
            record = self.station_records[station_id]
            status[station_id] = {
                'station_name': record.station_name,
                'state': state.value,
                'arrival_time': record.arrival_time.isoformat() if record.arrival_time else None,
                'stay_duration': record.stay_duration,
                'leave_time': record.leave_time.isoformat() if record.leave_time else None,
                'off_duration': record.off_duration
            }
        return status
    
    def reset_monitoring(self):
        """重置監控狀態"""
        self._initialize_stations()
        self.current_cycle_id = None
        self.cycle_start_time = None
        self.last_stay_trigger_time = None
        self.last_leave_trigger_time = None
        logger.info("監控狀態已重置")
