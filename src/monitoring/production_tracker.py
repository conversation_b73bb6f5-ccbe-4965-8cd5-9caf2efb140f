"""
生產追蹤器
負責追蹤整個生產週期和工站流程
"""

import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
from loguru import logger

from .station_monitor import StationRecord, StationEvent


@dataclass
class ProductionCycleData:
    """生產週期數據"""
    cycle_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_duration: Optional[float] = None
    stations_data: List[Dict[str, Any]] = field(default_factory=list)
    status: str = "RUNNING"  # RUNNING, COMPLETED, ABORTED
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'cycle_id': self.cycle_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'total_duration': self.total_duration,
            'stations_data': self.stations_data,
            'status': self.status
        }


class ProductionTracker:
    """生產追蹤器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化生產追蹤器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.production_timeout = config.get('monitoring', {}).get('production_timeout', 7200)
        
        # 生產週期管理
        self.current_cycle: Optional[ProductionCycleData] = None
        self.completed_cycles: deque = deque(maxlen=100)  # 保留最近100個週期
        
        # 統計信息
        self.total_cycles = 0
        self.total_production_time = 0.0
        self.average_cycle_time = 0.0
        self.min_cycle_time = float('inf')
        self.max_cycle_time = 0.0
        
        # 回調函數
        self.callbacks = {
            'on_cycle_timeout': [],
            'on_statistics_update': []
        }
        
        logger.info("生產追蹤器初始化完成")
    
    def add_callback(self, event_type: str, callback: Callable):
        """添加事件回調函數"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            logger.info(f"添加追蹤回調函數: {event_type}")
    
    def _trigger_callbacks(self, event_type: str, *args, **kwargs):
        """觸發回調函數"""
        for callback in self.callbacks.get(event_type, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"追蹤回調函數執行錯誤 ({event_type}): {e}")
    
    def start_cycle(self, cycle_id: str, start_time: datetime):
        """
        開始新的生產週期
        
        Args:
            cycle_id: 週期ID
            start_time: 開始時間
        """
        # 如果有未完成的週期，標記為中止
        if self.current_cycle and self.current_cycle.status == "RUNNING":
            logger.warning(f"中止未完成的週期: {self.current_cycle.cycle_id}")
            self.current_cycle.status = "ABORTED"
            self.completed_cycles.append(self.current_cycle)
        
        # 創建新週期
        self.current_cycle = ProductionCycleData(
            cycle_id=cycle_id,
            start_time=start_time
        )
        
        logger.info(f"開始新的生產週期: {cycle_id}")
    
    def end_cycle(self, cycle_id: str, end_time: datetime, total_duration: float):
        """
        結束生產週期
        
        Args:
            cycle_id: 週期ID
            end_time: 結束時間
            total_duration: 總持續時間
        """
        if not self.current_cycle or self.current_cycle.cycle_id != cycle_id:
            logger.warning(f"嘗試結束不存在或不匹配的週期: {cycle_id}")
            return
        
        # 更新週期數據
        self.current_cycle.end_time = end_time
        self.current_cycle.total_duration = total_duration
        self.current_cycle.status = "COMPLETED"
        
        # 添加到完成列表
        self.completed_cycles.append(self.current_cycle)
        
        # 更新統計信息
        self._update_statistics(total_duration)
        
        logger.info(f"生產週期完成: {cycle_id}, 耗時: {total_duration:.2f}秒")
        
        # 清除當前週期
        self.current_cycle = None
    
    def update_cycle_data(self, cycle_id: str, stations_data: List[Dict[str, Any]]):
        """
        更新週期的工站數據
        
        Args:
            cycle_id: 週期ID
            stations_data: 工站數據列表
        """
        if not self.current_cycle or self.current_cycle.cycle_id != cycle_id:
            logger.warning(f"嘗試更新不存在或不匹配的週期數據: {cycle_id}")
            return
        
        self.current_cycle.stations_data = stations_data
        logger.debug(f"更新週期數據: {cycle_id}, 工站數量: {len(stations_data)}")
    
    def _update_statistics(self, cycle_time: float):
        """
        更新統計信息
        
        Args:
            cycle_time: 週期時間
        """
        self.total_cycles += 1
        self.total_production_time += cycle_time
        self.average_cycle_time = self.total_production_time / self.total_cycles
        
        if cycle_time < self.min_cycle_time:
            self.min_cycle_time = cycle_time
        
        if cycle_time > self.max_cycle_time:
            self.max_cycle_time = cycle_time
        
        # 觸發統計更新回調
        self._trigger_callbacks('on_statistics_update', self.get_statistics())
    
    def check_timeout(self):
        """檢查生產超時"""
        if not self.current_cycle:
            return
        
        current_time = datetime.now()
        elapsed = (current_time - self.current_cycle.start_time).total_seconds()
        
        if elapsed > self.production_timeout:
            logger.warning(f"生產週期超時: {self.current_cycle.cycle_id}, 已運行{elapsed:.2f}秒")
            
            # 標記為中止
            self.current_cycle.status = "ABORTED"
            self.current_cycle.end_time = current_time
            self.current_cycle.total_duration = elapsed
            
            # 觸發超時回調
            self._trigger_callbacks('on_cycle_timeout', self.current_cycle.cycle_id, elapsed)
            
            # 移動到完成列表
            self.completed_cycles.append(self.current_cycle)
            self.current_cycle = None
    
    def get_current_cycle_info(self) -> Optional[Dict[str, Any]]:
        """獲取當前週期信息"""
        if not self.current_cycle:
            return None
        
        current_time = datetime.now()
        elapsed = (current_time - self.current_cycle.start_time).total_seconds()
        
        return {
            'cycle_id': self.current_cycle.cycle_id,
            'start_time': self.current_cycle.start_time.isoformat(),
            'elapsed_time': elapsed,
            'status': self.current_cycle.status,
            'stations_count': len(self.current_cycle.stations_data),
            'timeout_remaining': max(0, self.production_timeout - elapsed)
        }
    
    def get_recent_cycles(self, count: int = 10) -> List[Dict[str, Any]]:
        """
        獲取最近的週期記錄
        
        Args:
            count: 返回的週期數量
            
        Returns:
            週期記錄列表
        """
        recent = list(self.completed_cycles)[-count:]
        return [cycle.to_dict() for cycle in recent]
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計信息"""
        return {
            'total_cycles': self.total_cycles,
            'completed_cycles': len([c for c in self.completed_cycles if c.status == "COMPLETED"]),
            'aborted_cycles': len([c for c in self.completed_cycles if c.status == "ABORTED"]),
            'total_production_time': self.total_production_time,
            'average_cycle_time': self.average_cycle_time,
            'min_cycle_time': self.min_cycle_time if self.min_cycle_time != float('inf') else 0,
            'max_cycle_time': self.max_cycle_time,
            'current_cycle_active': self.current_cycle is not None,
            'production_timeout': self.production_timeout
        }
    
    def get_efficiency_metrics(self) -> Dict[str, Any]:
        """獲取效率指標"""
        if self.total_cycles == 0:
            return {
                'efficiency_score': 0,
                'completion_rate': 0,
                'average_throughput': 0,
                'time_utilization': 0
            }
        
        completed_count = len([c for c in self.completed_cycles if c.status == "COMPLETED"])
        completion_rate = (completed_count / self.total_cycles) * 100
        
        # 計算平均產能（每小時完成的週期數）
        if self.average_cycle_time > 0:
            average_throughput = 3600 / self.average_cycle_time  # 每小時
        else:
            average_throughput = 0
        
        # 計算時間利用率（實際生產時間 vs 理論最短時間）
        if self.min_cycle_time > 0 and self.min_cycle_time != float('inf'):
            time_utilization = (self.min_cycle_time / self.average_cycle_time) * 100
        else:
            time_utilization = 0
        
        # 綜合效率分數
        efficiency_score = (completion_rate + time_utilization) / 2
        
        return {
            'efficiency_score': round(efficiency_score, 2),
            'completion_rate': round(completion_rate, 2),
            'average_throughput': round(average_throughput, 2),
            'time_utilization': round(time_utilization, 2)
        }
    
    def reset_statistics(self):
        """重置統計信息"""
        self.total_cycles = 0
        self.total_production_time = 0.0
        self.average_cycle_time = 0.0
        self.min_cycle_time = float('inf')
        self.max_cycle_time = 0.0
        self.completed_cycles.clear()
        
        logger.info("生產追蹤統計信息已重置")
